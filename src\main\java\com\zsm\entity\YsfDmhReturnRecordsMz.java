package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 门诊-退药明细视图 V_YSF_DMH_RETURN_RECORDS_MZ
 */
@Data
@TableName("V_YSF_DMH_RETURN_RECORDS_MZ")
@Schema(description = "门诊-退药明细视图")
public class YsfDmhReturnRecordsMz implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("MED_LIST_CODG")
    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @TableField("FIXMEDINS_HILIST_ID")
    @Schema(description = "定点医药机构目录编号（HIS药品唯一值）")
    private String fixmedinsHilistId;

    @TableField("FIXMEDINS_HILIST_NAME")
    @Schema(description = "定点医药机构目录名称（HIS药品名称）")
    private String fixmedinsHilistName;

    @TableField("FIXMEDINS_BCHNO")
    @Schema(description = "定点医药机构批次流水号（本条记录唯一值）")
    private String fixmedinsBchno;

    @TableField("PRSC_DR_CERTNO")
    @Schema(description = "开方医师证件号码")
    private String prscDrCertno;

    @TableField("PRSC_DR_NAME")
    @Schema(description = "开方医师姓名")
    private String prscDrName;

    @TableField("PHAR_CERTNO")
    @Schema(description = "药师证件号码")
    private String pharCertno;

    @TableField("PHAR_NAME")
    @Schema(description = "药师姓名")
    private String pharName;

    @TableField("PHAR_PRAC_CERT_NO")
    @Schema(description = "药师执业资格证号")
    private String pharPracCertNo;

    @TableField("MDTRT_SN")
    @Schema(description = "就医流水号（医保：MDTRT_ID，自费：院内流水号）")
    private String mdtrtSn;

    @TableField("PSN_NAME")
    @Schema(description = "人员姓名")
    private String psnName;

    @TableField("MANU_LOTNUM")
    @Schema(description = "生产批号")
    private String manuLotnum;

    @TableField("MANU_DATE")
    @Schema(description = "生产日期 yyyy-MM-dd")
    private String manuDate;

    @TableField("EXPY_END")
    @Schema(description = "有效期止 yyyy-MM-dd")
    private String expyEnd;

    @TableField("RX_FLAG")
    @Schema(description = "处方药标志 0-否 1-是")
    private String rxFlag;

    @TableField("TRDN_FLAG")
    @Schema(description = "拆零标志 0-否 1-是")
    private String trdnFlag;

    @TableField("RXNO")
    @Schema(description = "处方号")
    private String rxno;

    @TableField("RX_CIRC_FLAG")
    @Schema(description = "外购处方标志")
    private String rxCircFlag;

    @TableField("RTAL_DOCNO")
    @Schema(description = "零售单据号")
    private String rtalDocno;

    @TableField("STOOUT_NO")
    @Schema(description = "销售出库单据号")
    private String stooutNo;

    @TableField("BCHNO")
    @Schema(description = "批次号")
    private String bchno;

    @TableField("SEL_RETN_CNT")
    @Schema(description = "销售/退货数量")
    private String selRetnCnt;

    @TableField("SEL_RETN_UNIT")
    @Schema(description = "发药时候的单位")
    private String selRetnUnit;

    @TableField("HIS_DOS_UNIT")
    @Schema(description = "HIS 的剂量单位")
    private String hisDosUnit;

    @TableField("HIS_PAC_UNIT")
    @Schema(description = "HIS 的包装单位")
    private String hisPacUnit;

    @TableField("SEL_RETN_TIME")
    @Schema(description = "销售/退货时间 yyyy-MM-dd HH:mm:ss")
    private String selRetnTime;

    @TableField("SEL_RETN_OPTER_NAME")
    @Schema(description = "销售/退货经办人姓名")
    private String selRetnOpterName;

    @TableField("MDTRT_SETL_TYPE")
    @Schema(description = "就诊结算类型 1-医保结算 2-自费结算")
    private String mdtrtSetlType;

    @TableField("SPEC")
    @Schema(description = "规格")
    private String spec;

    @TableField("PRODENTP_NAME")
    @Schema(description = "生产企业名称")
    private String prodentpName;

    @TableField("CFXH")
    @Schema(description = "处方序号")
    private String cfxh;

    @TableField("CFMXXH")
    @Schema(description = "处方明细序号")
    private String cfmxxh;

    @TableField("SJH")
    @Schema(description = "收据号（处方小票扫码值）")
    private String sjh;

    @TableField("PATIENT_ID")
    @Schema(description = "患者ID")
    private String patientId;

    @TableField("HIS_CON_RATIO")
    @Schema(description = "最小单位转换比")
    private String hisConRatio;

    @TableField("ORI_CFXH")
    @Schema(description = "原处方序号（退药关联原发药）")
    private String oriCfxh;

    @TableField("ORI_CFMXXH")
    @Schema(description = "原处方明细序号（退药关联原发药）")
    private String oriCfmxxh;

    @TableField("PSN_NO")
    @Schema(description = "人员编号（医保人员编号）")
    private String psnNo;
}


