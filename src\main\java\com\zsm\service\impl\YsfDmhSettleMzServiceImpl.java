package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhSettleMz;
import com.zsm.mapper.YsfDmhSettleMzMapper;
import com.zsm.service.YsfDmhSettleMzService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhSettleMzServiceImpl extends ServiceImpl<YsfDmhSettleMzMapper, YsfDmhSettleMz> implements YsfDmhSettleMzService {
}


