package com.zsm.utils;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsm.common.exception.SaasHttpException;
import com.zsm.model.dto.PrescriptionTrackingLogListRequest;
import com.zsm.model.saas.request.ConfirmDispDrugRequest;
import com.zsm.model.saas.request.GetTracCodgStoreRequest;
import com.zsm.model.saas.request.HisDrugInfoSaasRequest;
import com.zsm.model.saas.request.QueryTracDrugRequest;
import com.zsm.model.saas.response.*;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.service.TokenCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * saas接口请求工具服务类
 *
 * <AUTHOR>
 * @date 2025/06/06
 */
@Slf4j
@Component
public class SaasHttpUtil {

    private static ApplicationContext applicationContext;

    /**
     * 设置ApplicationContext，用于在静态方法中获取Bean
     */
    @Autowired
    public void setApplicationContext(ApplicationContext context) {
        SaasHttpUtil.applicationContext = context;
    }

    private final static String accessToken = "/drug/interface/medins/accessToken";
    private final static String getInfo = "/system/user/getInfo";
    private final static String getDrugIdCode = "/system/interface/medins/getDrugIdCode";
    private final static String uploadPrescriptionTrackingLogList = "/purc/prescriptionTrackingLog/uploadPrescriptionTrackingLogList";
    private final static String queryTracDrug = "/system/interface/pda/queryTracDrug";
    private final static String queryTracDrugYz = "/system/interface/pda/queryTracDrugYz";
    private final static String getTracCodgStore = "/system/interface/pda/getTracCodgStore";
    private final static String confirmDispDrug = "/system/interface/pda/confirmDispDrug";
    private final static String uploadHisDrugInfo = "/system/interface/medins/importDrug";
    private final static String uploadHisEnterpriseInfo = "/system/interface/medins/importEntp";
    private final static String SAAS_TOKEN = "saas:token:";
    private final static Integer SAAS_TOKEN_EXPIRE = 12;

    /**
     * TODO:saas接口基础地址,如果院内的网络环境受限,那么修改为可以访问的接口地址
     */
    // private final static String saasBaseUrl = "http://*************:30800/saas-prod/prod";
    private final static String saasBaseUrl = "http://***************:30800/saas-prod/prod";

    // HTTP配置静态常量
    /**
     * 请求超时时间（毫秒）
     */
    private static final Integer TIMEOUT = 60000;
    
    /**
     * 最大重试次数
     */
    private static final Integer MAX_RETRY_TIMES = 3;
    
    /**
     * 重试延迟时间（毫秒）
     */
    private static final Integer RETRY_DELAY_MS = 1000;
    
    /**
     * 是否启用重试机制
     */
    private static final Boolean ENABLE_RETRY = true;

    /**
     * 通用HTTP POST请求方法
     */
    private static HttpResponse executePostRequest(String endpoint, Object requestBody, Map<String, String> headers, String logPrefix) {
        String url = saasBaseUrl + endpoint;
        String requestBodyStr = requestBody != null ? JSONUtil.toJsonStr(requestBody) : null;

        log.info("{} 请求地址：{}", logPrefix, url);

        if (headers != null && !headers.isEmpty()) {
            log.info("{} 请求头：{}", logPrefix, JSONUtil.toJsonStr(headers));
        }

        if (requestBodyStr != null) {
            log.info("{} 请求数据：{}", logPrefix, requestBodyStr);
        }

        try {
            HttpRequest request = HttpRequest.post(url).timeout(TIMEOUT);

            if (headers != null && !headers.isEmpty()) {
                request.headerMap(headers, false);
            }

            if (requestBodyStr != null) {
                request.body(requestBodyStr);
            }

            HttpResponse response = request.execute();
            log.info("{} 响应数据：{}", logPrefix, response.body());

            // 检查HTTP状态码
            if (response.getStatus() < 200 || response.getStatus() >= 300) {
                throw new SaasHttpException(
                        String.format("%s HTTP请求失败，状态码: %d", logPrefix, response.getStatus()),
                        endpoint,
                        requestBodyStr,
                        response.body(),
                        response.getStatus()
                );
            }

            return response;

        } catch (SaasHttpException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 请求异常：{}", logPrefix, e.getMessage(), e);
            throw new SaasHttpException(
                    String.format("%s 请求失败: %s", logPrefix, e.getMessage()),
                    endpoint,
                    requestBodyStr,
                    null,
                    null,
                    e
            );
        }
    }

    /**
     * 通用HTTP GET请求方法
     */
    private static HttpResponse executeGetRequest(String endpoint, Map<String, String> headers, String logPrefix) {
        String url = saasBaseUrl + endpoint;
        log.info("{} 请求地址：{}", logPrefix, url);

        if (headers != null && !headers.isEmpty()) {
            log.info("{} 请求头：{}", logPrefix, JSONUtil.toJsonStr(headers));
        }

        try {
            HttpRequest request = HttpRequest.get(url).timeout(TIMEOUT);

            if (headers != null && !headers.isEmpty()) {
                request.headerMap(headers, false);
            }

            HttpResponse response = request.execute();
            log.info("{} 响应数据：{}", logPrefix, response.body());

            // 检查HTTP状态码
            if (response.getStatus() < 200 || response.getStatus() >= 300) {
                throw new SaasHttpException(
                        String.format("%s HTTP请求失败，状态码: %d", logPrefix, response.getStatus()),
                        endpoint,
                        null,
                        response.body(),
                        response.getStatus()
                );
            }

            return response;

        } catch (SaasHttpException e) {
            throw e;
        } catch (Exception e) {
            log.error("{} 请求异常：{}", logPrefix, e.getMessage(), e);
            throw new SaasHttpException(
                    String.format("%s 请求失败: %s", logPrefix, e.getMessage()),
                    endpoint,
                    null,
                    null,
                    null,
                    e
            );
        }
    }

    /**
     * 创建认证请求头
     */
    private static Map<String, String> createAuthHeaders(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", token);
        headers.put("Access-Token", token);
        return headers;
    }

    /**
     * 验证响应结果并记录错误
     */
    private static boolean validateResponse(JSONObject response, String logPrefix, Object request, String responseBody, String codeField, Object expectedCode) {
        Object actualCode = response.get(codeField);
        if (!Objects.equals(actualCode, expectedCode)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, responseBody);
            return false;
        }
        return true;
    }

    /**
     * 带重试机制的HTTP请求执行
     */
    private static HttpResponse executeWithRetry(String endpoint, Object requestBody, Map<String, String> headers, String logPrefix, boolean isPost) {
        Exception lastException = null;

        // 如果禁用重试，直接执行一次
        int maxRetries = ENABLE_RETRY ? MAX_RETRY_TIMES : 1;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 1) {
                    log.info("{} 第{}次重试请求", logPrefix, attempt);
                    Thread.sleep((long) RETRY_DELAY_MS * attempt); // 递增延迟
                }

                if (isPost) {
                    return executePostRequest(endpoint, requestBody, headers, logPrefix);
                } else {
                    return executeGetRequest(endpoint, headers, logPrefix);
                }

            } catch (SaasHttpException e) {
                lastException = e;
                // 如果是4xx错误，不重试
                if (e.getHttpStatus() != null && e.getHttpStatus() >= 400 && e.getHttpStatus() < 500) {
                    log.warn("{} 客户端错误，不进行重试：{}", logPrefix, e.getMessage());
                    throw e;
                }

                if (attempt == maxRetries) {
                    log.error("{} 重试{}次后仍然失败", logPrefix, maxRetries);
                    throw e;
                } else {
                    log.warn("{} 第{}次请求失败，准备重试：{}", logPrefix, attempt, e.getMessage());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new SaasHttpException(String.format("%s 请求被中断", logPrefix), e);
            } catch (Exception e) {
                lastException = e;
                if (attempt == maxRetries) {
                    log.error("{} 重试{}次后仍然失败", logPrefix, maxRetries);
                    throw new SaasHttpException(String.format("%s 请求失败", logPrefix), e);
                } else {
                    log.warn("{} 第{}次请求失败，准备重试：{}", logPrefix, attempt, e.getMessage());
                }
            }
        }

        // 理论上不会到达这里
        throw new SaasHttpException(String.format("%s 请求失败", logPrefix), lastException);
    }

    /**
     * 安全解析JSON响应
     */
    private static <T> T parseResponse(String responseBody, Class<T> clazz, String logPrefix) {
        try {
            return JSONUtil.toBean(responseBody, clazz);
        } catch (Exception e) {
            log.error("{} JSON解析失败，响应数据：{}", logPrefix, responseBody, e);
            throw new SaasHttpException(String.format("%s JSON解析失败: %s", logPrefix, e.getMessage()), e);
        }
    }

    /**
     * 获取访问令牌（集成缓存功能，保持原有调用方式）
     * 先从缓存中获取，如果缓存不存在或已过期，则重新获取并缓存到当天24点
     *
     * @param userAccount  帐户
     * @param userPassWord 密码
     * @return {@link AccessTokenReponse }
     */
    public static AccessTokenReponse getAccessToken(String userAccount, String userPassWord) {
        String logPrefix = "获取访问令牌(带缓存)";
        
        try {
            // 尝试通过Spring容器获取TokenCacheService
            if (applicationContext != null) {
                TokenCacheService tokenCacheService = applicationContext.getBean(TokenCacheService.class);
                
                // 先尝试从缓存中获取
                AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache(userAccount);
                if (cachedToken != null && Objects.equals(cachedToken.getReturnCode(), 0)) {
                    log.info("{} 从缓存中获取到有效Token, userAccount: {}", logPrefix, userAccount);
                    return cachedToken;
                }
                
                // 缓存中没有或已过期，重新获取
                log.info("{} 缓存中没有有效Token，重新获取, userAccount: {}", logPrefix, userAccount);
                AccessTokenReponse newToken = getAccessTokenFromServer(userAccount, userPassWord);
                
                // 如果获取成功，则缓存到当天24点
                if (newToken != null && Objects.equals(newToken.getReturnCode(), 0)) {
                    tokenCacheService.cacheToken(userAccount, newToken);
                    log.info("{} 新Token已缓存, userAccount: {}", logPrefix, userAccount);
                }
                
                return newToken;
            } else {
                // 如果Spring容器不可用，降级为直接从服务器获取
                log.warn("{} Spring容器不可用，降级为直接获取Token, userAccount: {}", logPrefix, userAccount);
                return getAccessTokenFromServer(userAccount, userPassWord);
            }
        } catch (Exception e) {
            // 如果缓存服务出现问题，降级为直接从服务器获取
            log.error("{} 缓存服务异常，降级为直接获取Token, userAccount: {}", logPrefix, userAccount, e);
            return getAccessTokenFromServer(userAccount, userPassWord);
        }
    }

    /**
     * 从服务器获取token（原始实现）
     *
     * @param userAccount  帐户
     * @param userPassWord 密码
     * @return {@link AccessTokenReponse }
     */
    private static AccessTokenReponse getAccessTokenFromServer(String userAccount, String userPassWord) {
        String logPrefix = "登录获取token";
        Map<String, String> request = new HashMap<>();
        request.put("userAccount", userAccount);
        request.put("userPassWord", userPassWord);

        HttpResponse response = executeWithRetry(accessToken, request, null, logPrefix, true);
        AccessTokenReponse result = parseResponse(response.body(), AccessTokenReponse.class, logPrefix);

        if (!Objects.equals(result.getReturnCode(), 0)) {
            log.error("{} 业务异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
        }

        return result;
    }

    public static SaasUserInfoResponse getInfo(String authorization) {
        try {
            String logPrefix = "getInfo";
            Map<String, String> headers = createAuthHeaders(authorization);

            HttpResponse response = executeWithRetry(getInfo, null, headers, logPrefix, false);
            SaasUserInfoResponse result = parseResponse(response.body(), SaasUserInfoResponse.class, logPrefix);
            result.setAuthorization(authorization);
            return result;

        } catch (Exception e) {
            log.error("getInfo 异常：{}", e.getMessage(), e);
            return null;
        }
    }

    public static JSONObject getDrugIdCode(JSONObject request, String token) {
        String logPrefix = "getDrugIdCode";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(getDrugIdCode, request, headers, logPrefix);
        JSONObject result = JSONUtil.parseObj(response.body());

        validateResponse(result, logPrefix, request, response.body(), "returnCode", 0);
        return result;
    }

    public static JSONObject uploadPrescriptionTrackingLogList(PrescriptionTrackingLogListRequest request, String token) {
        String logPrefix = "uploadPrescriptionTrackingLogList";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(uploadPrescriptionTrackingLogList, request, headers, logPrefix);
        JSONObject result = JSONUtil.parseObj(response.body());

        validateResponse(result, logPrefix, request, response.body(), "code", 200);
        return result;
    }

    /**
     * 调用SaaS接口批量查询`药品拆零`追溯信息
     *
     * @param token       令牌
     * @param requestList 请求列表
     * @return {@link Map }<{@link String }, {@link QueryTracDrugResponse }>
     */
    public static Map<String, QueryTracDrugResponse> queryTracDrugMap(String token, List<QueryTracDrugRequest> requestList) {
        Map<String, QueryTracDrugResponse> map = new HashMap<>();
        if (requestList != null && !requestList.isEmpty()) {
            List<QueryTracDrugResponse> tracCodgStore = queryTracDrug(token, requestList);
            map = tracCodgStore.stream()
                    .collect(Collectors.toMap(QueryTracDrugResponse::getCfmxxh, Function.identity(), (o, n) -> n));
        }
        return map;
    }

    public static List<QueryTracDrugResponse> queryTracDrug(String token, List<QueryTracDrugRequest> request) {
        String logPrefix = "queryTracDrug";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(queryTracDrug, request, headers, logPrefix);
        SaasCommmonListResponse<QueryTracDrugResponse> result = JSONUtil.toBean(response.body(), new TypeReference<SaasCommmonListResponse<QueryTracDrugResponse>>() {
        }, false);

        if (!Objects.equals(result.getCode(), 200)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
            return new ArrayList<>();
        }
        return result.getData();
    }

    /**
     * 调用SaaS接口批量查询`药品(混发)拆零`追溯信息
     *
     * @param token       令牌
     * @param requestList 请求列表
     * @return {@link Map }<{@link String }, {@link QueryTracDrugResponse }>
     */
    public static Map<String, QueryTracDrugResponse> queryTracDrugYzMap(String token, List<QueryTracDrugRequest> requestList) {
        Map<String, QueryTracDrugResponse> map = new HashMap<>();
        if (requestList != null && !requestList.isEmpty()) {
            List<QueryTracDrugResponse> tracCodgStore = queryTracDrugYz(token, requestList);
            map = tracCodgStore.stream()
                    .collect(Collectors.toMap(QueryTracDrugResponse::getCfmxxh, Function.identity(), (o, n) -> n));
        }
        return map;
    }

    public static List<QueryTracDrugResponse> queryTracDrugYz(String token, List<QueryTracDrugRequest> request) {
        String logPrefix = "queryTracDrugYz";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(queryTracDrugYz, request, headers, logPrefix);
        SaasCommmonListResponse<QueryTracDrugResponse> result = JSONUtil.toBean(response.body(), new TypeReference<SaasCommmonListResponse<QueryTracDrugResponse>>() {
        }, false);

        if (!Objects.equals(result.getCode(), 200)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
            return new ArrayList<>();
        }
        return result.getData();
    }

    public static List<GetTracCodgStoreDataResponse> getTracCodgStore(String token, GetTracCodgStoreRequest request) {
        String logPrefix = "getTracCodgStore";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(getTracCodgStore, request, headers, logPrefix);
        SaasCommmonListResponse<GetTracCodgStoreDataResponse> result = JSONUtil.toBean(response.body(), new TypeReference<SaasCommmonListResponse<GetTracCodgStoreDataResponse>>() {
        }, false);

        if (!Objects.equals(result.getCode(), 200)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
            return new ArrayList<>();
        }
        return result.getData();
    }

    public static List<ConfirmDispDrugDataResponse> confirmDispDrug(String token, ConfirmDispDrugRequest request) {
        String logPrefix = "confirmDispDrug";
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executePostRequest(confirmDispDrug, request, headers, logPrefix);
        SaasCommmonListResponse<ConfirmDispDrugDataResponse> result = JSONUtil.toBean(response.body(), new TypeReference<SaasCommmonListResponse<ConfirmDispDrugDataResponse>>() {
        }, false);

        if (!Objects.equals(result.getCode(), 200)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
            return new ArrayList<>();
        }
        return result.getData();
    }
    public static SaasCommmonListResponse<UploadHisDrugInfoResponse> uploadHisDrugInfo(String token, HisDrugInfoSaasRequest request) {
        String logPrefix = "上传his药品字典";
        
        // 设置请求ID
        String uuid = UUID.randomUUID().toString().replace("-", "");
        request.setRequestID(uuid);
        
        Map<String, String> headers = createAuthHeaders(token);

        HttpResponse response = executeWithRetry(uploadHisDrugInfo, request, headers, logPrefix, true);
        SaasCommmonListResponse<UploadHisDrugInfoResponse> result = JSONUtil.toBean(response.body(), new TypeReference<SaasCommmonListResponse<UploadHisDrugInfoResponse>>() {
        }, false);

        if (!Objects.equals(result.getReturnCode(), 0)) {
            log.error("{} 异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{} 响应数据：{}", logPrefix, response.body());
        }
        
        return result;
    }

}
