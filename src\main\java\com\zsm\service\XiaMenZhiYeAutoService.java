package com.zsm.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfDmhLayRecordsZy;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.ConfirmDispDrugDataRequest;
import com.zsm.model.saas.request.ConfirmDispDrugRequest;
import com.zsm.model.saas.request.GetTracCodgStoreDataRequest;
import com.zsm.model.saas.request.GetTracCodgStoreRequest;
import com.zsm.model.saas.request.QueryTracDrugRequest;
import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.InPatientDispenseDetailVo;
import com.zsm.model.vo.InpatientSettlementVo;
import com.zsm.utils.DateRangeUtil;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;
import com.zsm.utils.SaasHttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 厦门智业视图业务处理服务类
 *
 * 目标：基于视图 V_YSF_DMH_LAY_RECORDS_ZY 实现住院追溯码补录流程，并与原逻辑保持一致
 */
@Slf4j
@Service
public class XiaMenZhiYeAutoService {

    @Resource
    private YsfDmhLayRecordsZyService ysfDmhLayRecordsZyService;
    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private TokenCacheService tokenCacheService;
    @Resource
    private NhsaRetryUtil nhsaRetryUtil;

    // 直接委托旧服务以避免重复实现药典同步
    @Resource
    private HangChuangService hangChuangService;

    /**
     * 同步HIS药品字典（委托旧服务实现）
     */
    public ApiResult<String> syncDrugDictionary() {
        return hangChuangService.syncDrugDictionary();
    }

    /**
     * 住院患者追溯码补录（视图版）
     * 与原逻辑一致：查询结算患者 → 查5204 → 查住院发药明细 → 保存3505 → 赋码确认 → 上传
     */
    public ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate) {
        DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
        log.info("[XMZY] 住院追溯码补录统计周范围：{} 至 {}", weeklyRange.getStartDate(), weeklyRange.getEndDate());
        try {
            List<InpatientSettlementVo> inpatients = queryInpatientsListFromView(startDate, endDate);
            if (inpatients.isEmpty()) {
                return ApiResult.success("指定时间范围内无住院结算患者");
            }
            return batchProcessInpatientTraceability(inpatients, weeklyRange);
        } catch (Exception e) {
            log.error("[XMZY] 住院追溯码补录异常", e);
            return ApiResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 从视图聚合“结算患者”列表（按住院号去重）
     */
    private List<InpatientSettlementVo> queryInpatientsListFromView(String startDate, String endDate) {
        try {
            String start = startDate + " 00:00:00";
            String end = endDate + " 23:59:59";
            LambdaQueryWrapper<YsfDmhLayRecordsZy> qw = new LambdaQueryWrapper<>();
            // 使用结算时间过滤
            qw.between(YsfDmhLayRecordsZy::getSetlTime, start, end);
            List<YsfDmhLayRecordsZy> raw = ysfDmhLayRecordsZyService.list(qw);
            if (raw == null || raw.isEmpty()) {
                return Collections.emptyList();
            }
            Map<String, YsfDmhLayRecordsZy> byPatInHos = raw.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getPatInHosId()))
                    .collect(Collectors.toMap(YsfDmhLayRecordsZy::getPatInHosId, Function.identity(), (o, n) -> o));
            List<InpatientSettlementVo> list = new ArrayList<>();
            for (YsfDmhLayRecordsZy item : byPatInHos.values()) {
                InpatientSettlementVo vo = new InpatientSettlementVo();
                vo.setPatInHosId(item.getPatInHosId());
                vo.setPsnNo(item.getPsnNo());
                vo.setMdtrtSn(item.getMdtrtSn());
                vo.setPatientName(item.getPsnName());
                vo.setSelTime(item.getSetlTime());
                list.add(vo);
            }
            log.info("[XMZY] 视图聚合结算患者数：{}", list.size());
            return list;
        } catch (Exception e) {
            log.error("[XMZY] 视图查询结算患者异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量处理住院患者的追溯码补录
     */
    private ApiResult<String> batchProcessInpatientTraceability(List<InpatientSettlementVo> inpatients, DateRange weeklyRange) {
        int successPatients = 0;
        int failedPatients = 0;
        StringBuilder errorMessages = new StringBuilder();
        int total = inpatients.size();
        log.info("[XMZY] 批量处理住院追溯码补录，共 {} 位患者", total);
        for (InpatientSettlementVo patient : inpatients) {
            try {
                boolean ok = processInpatientWithWeeklyRange(patient, weeklyRange);
                if (ok) {
                    successPatients++;
                } else {
                    failedPatients++;
                    errorMessages.append("患者").append(patient.getPatientName()).append("处理失败; ");
                }
            } catch (Exception ex) {
                failedPatients++;
                log.error("[XMZY] 处理患者异常: {}", patient.getPatientName(), ex);
                errorMessages.append("患者").append(patient.getPatientName()).append("异常: ").append(ex.getMessage()).append("; ");
            }
            int processed = successPatients + failedPatients;
            int remaining = total - processed;
            log.info("[XMZY] 进度: 已处理 {}/{}，剩余 {} (成功: {}，失败: {})", processed, total, remaining, successPatients, failedPatients);
        }
        String msg = String.format("处理完成，成功: %d, 失败: %d", successPatients, failedPatients);
        if (!errorMessages.isEmpty()) {
            msg += "，错误详情: " + errorMessages;
        }
        return ApiResult.success(msg);
    }

    /**
     * 按统计周范围处理单个患者
     */
    private boolean processInpatientWithWeeklyRange(InpatientSettlementVo patient, DateRange weeklyRange) {
        String name = patient.getPatientName();
        try {
            // 1) 查询5204（药品）并按统计周过滤
            List<Fsi5204Response> feeDetails = queryFeeDetails(patient);
            log.info("[XMZY] 患者 {} 查询5204费用 {} 条", name, feeDetails.size());
            feeDetails = feeDetails.stream()
                    .filter(item -> isWithinDateRange(item.getFeeOcurTime(), weeklyRange.getStartDate(), weeklyRange.getEndDate()))
                    .collect(Collectors.toList());
            log.info("[XMZY] 患者 {} 统计周内5204费用 {} 条", name, feeDetails.size());
            if (feeDetails.isEmpty()) {
                return true;
            }
            // 2) 视图查询住院发药明细
            List<InPatientDispenseDetailBindScatteredVo> dispenseDetails = queryPatientDispenseDetailsFromView(patient, weeklyRange);
            log.info("[XMZY] 患者 {} 发药明细 {} 条", name, dispenseDetails.size());
            if (dispenseDetails.isEmpty()) {
                return true;
            }
            // 2.1) 用5204对齐过滤
            dispenseDetails = filterInpatientDispensingData(feeDetails, dispenseDetails);
            // 2.5) 增量保存到3505
            try {
                nhsa3505Service.saveInpatientDataToNhsa3505(dispenseDetails);
                log.info("[XMZY] 患者 {} 保存住院发药明细到3505: {} 条", name, dispenseDetails.size());
            } catch (Exception e) {
                log.warn("[XMZY] 患者 {} 保存3505异常(不中断): {}", name, e.getMessage());
            }
            // 3) 查询现存3505并识别未同步数据
            List<Nhsa3505> existing3505 = queryExisting3505Data(patient);
            List<Nhsa3505> unSynced = identifyUnSyncedDataWithFeeDetails(feeDetails, existing3505);
            if (unSynced.isEmpty()) {
                return true;
            }
            // 4) 赋码并拆零确认
            boolean traceAssignedOk = processTraceCodeAssignment(unSynced, dispenseDetails, name);
            if (!traceAssignedOk) {
                log.error("[XMZY] 患者 {} 追溯码赋值失败", name);
                return false;
            }
            // 5) 上传未同步数据
            return processUnSyncedDataUpload(unSynced, patient);
        } catch (Exception e) {
            log.error("[XMZY] 处理患者 {} 异常", name, e);
            return false;
        }
    }

    /**
     * 从视图查询单个患者的住院发药明细，转换为扩展VO
     */
    private List<InPatientDispenseDetailBindScatteredVo> queryPatientDispenseDetailsFromView(InpatientSettlementVo patient, DateRange weeklyRange) {
        try {
            LambdaQueryWrapper<YsfDmhLayRecordsZy> qw = new LambdaQueryWrapper<>();
            qw.eq(YsfDmhLayRecordsZy::getPatInHosId, patient.getPatInHosId());
            String start = weeklyRange.getStartDate() + " 00:00:00";
            String end = weeklyRange.getEndDate() + " 23:59:59";
            qw.between(YsfDmhLayRecordsZy::getSelRetnTime, start, end);
            List<YsfDmhLayRecordsZy> zyList = ysfDmhLayRecordsZyService.list(qw);
            if (zyList == null || zyList.isEmpty()) {
                return Collections.emptyList();
            }
            List<InPatientDispenseDetailBindScatteredVo> result = new ArrayList<>();
            for (YsfDmhLayRecordsZy z : zyList) {
                InPatientDispenseDetailBindScatteredVo v = new InPatientDispenseDetailBindScatteredVo();
                // 基础字段
                v.setRecordId(z.getRecordId());
                // v.setRecordDetailId(z.getRecordDetailId());
                // v.setOriDetailId(z.getOriDetailId());
                // 将视图的医嘱ID映射到 idFee（用于与5204.rx_drord_no 对齐）
                // v.setIdFee(z.getOrderId());
                // v.setNaFee(z.getNaFee());
                // v.setSdClassify(z.getSdClassify());
                // v.setFgDps(z.getFgDps());
                v.setSendFlag(z.getSendFlag());
                v.setSendTime(z.getSendTime());
                v.setRtalDocno(z.getRtalDocno());
                v.setStooutNo(z.getStooutNo());
                // v.setPatWardId(z.getPatWardId());
                // v.setPatWardName(z.getPatWardName());
                // fyyf 采用视图中的发药科室ID（对齐账号枚举中的药房ID）
                v.setFyyf(z.getDeptId());
                v.setPharCertno(z.getPharCertno());
                v.setPharName(z.getPharName());
                v.setPharPracCertNo(z.getPharPracCertNo());
                v.setSelRetnTime(z.getSelRetnTime());
                // 药品信息
                // v.setHisDrugCode(z.getHisDrugCode());
                v.setMedListCodg(z.getMedListCodg());
                v.setSpec(z.getSpec());
                v.setProdentpName(z.getProdentpName());
                v.setFixmedinsHilistId(z.getFixmedinsHilistId());
                v.setFixmedinsHilistName(z.getFixmedinsHilistName());
                v.setManuLotnum(z.getManuLotnum());
                v.setManuDate(z.getManuDate());
                v.setExpyEnd(z.getExpyEnd());
                v.setBchno(z.getBchno());
                v.setRxFlag(z.getRxFlag());
                v.setTrdnFlag(z.getTrdnFlag());
                v.setHisDosUnit(z.getHisDosUnit());
                v.setHisPacUnit(z.getHisPacUnit());
                v.setHisConRatio(z.getHisConRatio());
                // 患者与就诊
                v.setFixmedinsBchno(z.getFixmedinsBchno());
                v.setPatInHosId(z.getPatInHosId());
                v.setMdtrtSn(z.getMdtrtSn());
                v.setPsnNo(z.getPsnNo());
                v.setPsnName(z.getPsnName());
                // v.setBedNo(z.getBedNo());
                v.setMdtrtSetlType(z.getMdtrtSetlType());
                // v.setOrderId(z.getOrderId());
                v.setPrscDrCertno(z.getPrscDrCertno());
                v.setPrscDrName(z.getPrscDrName());
                // 数量
                if (z.getSelRetnCnt() != null) {
                    v.setSelRetnCnt(z.getSelRetnCnt());
                    v.setDispCnt(String.valueOf(z.getSelRetnCnt()));
                }
                v.setSelRetnUnit(z.getSelRetnUnit());
                v.setCfxh(z.getCfxh());
                v.setCfmxxh(String.valueOf(z.getCfmxxh()));
                result.add(v);
            }
            return result;
        } catch (Exception e) {
            log.error("[XMZY] 视图查询患者住院发药明细异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 过滤住院发药数据：通过5204.rx_drord_no 与 发药明细的 idFee 关联
     */
    private List<InPatientDispenseDetailBindScatteredVo> filterInpatientDispensingData(List<Fsi5204Response> feeDetails,
                                                                                       List<InPatientDispenseDetailBindScatteredVo> inpatientDetails) {
        Set<String> rxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<InPatientDispenseDetailBindScatteredVo> filtered = inpatientDetails.stream()
                .filter(x -> StringUtils.isNotBlank(x.getIdFee()))
                .filter(x -> rxNos.contains(x.getIdFee()))
                .collect(Collectors.toList());
        log.info("[XMZY] 5204对齐后需处理的住院明细 {} 条", filtered.size());
        return filtered;
    }

    /**
     * 查询已存在的3505数据
     */
    private List<Nhsa3505> queryExisting3505Data(InpatientSettlementVo patient) {
        try {
            LambdaQueryWrapper<Nhsa3505> qw = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(patient.getMdtrtSn())) {
                qw.eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn());
            } else {
                qw.eq(Nhsa3505::getPatientId, patient.getPatInHosId());
            }
            return nhsa3505Service.list(qw);
        } catch (Exception e) {
            log.error("[XMZY] 查询已存在3505数据异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 识别需要赋码上传的未同步数据（feedetl_sn 在 5204.rx_drord_no 中 且 hsa_sync_status=0）
     */
    private List<Nhsa3505> identifyUnSyncedDataWithFeeDetails(List<Fsi5204Response> feeDetails,
                                                               List<Nhsa3505> existing3505Data) {
        Set<String> rxNos = feeDetails.stream()
                .map(Fsi5204Response::getRxDrordNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<Nhsa3505> unSynced = existing3505Data.stream()
                .filter(d -> "0".equals(d.getHsaSyncStatus()))
                .filter(d -> StringUtils.isNotBlank(d.getFeedetlSn()) && rxNos.contains(d.getFeedetlSn()))
                .collect(Collectors.toList());
        log.info("[XMZY] 识别到未同步3505数据 {} 条", unSynced.size());
        return unSynced;
    }

    /**
     * 追溯码赋值与拆零确认（按 fyyf 分组）
     */
    private boolean processTraceCodeAssignment(List<Nhsa3505> unSyncedData,
                                               List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails,
                                               String patientName) {
        try {
            Set<String> unSyncedBchno = unSyncedData.stream()
                    .filter(d -> StrUtil.isBlank(d.getDrugTracInfo()))
                    .map(Nhsa3505::getFixmedinsBchno)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            List<InPatientDispenseDetailBindScatteredVo> matched = patientDispenseDetails.stream()
                    .filter(x -> unSyncedBchno.contains(x.getFixmedinsBchno()))
                    .collect(Collectors.toList());
            Map<String, List<InPatientDispenseDetailBindScatteredVo>> byFyyf = matched.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getFyyf()))
                    .collect(Collectors.groupingBy(InPatientDispenseDetailVo::getFyyf));
            int totalAssigned = 0;
            for (Map.Entry<String, List<InPatientDispenseDetailBindScatteredVo>> entry : byFyyf.entrySet()) {
                String fyyf = entry.getKey();
                List<InPatientDispenseDetailBindScatteredVo> groupDetails = entry.getValue();
                String token = getTokenByFyyf(fyyf);
                if (StrUtil.isBlank(token)) {
                    log.error("[XMZY] 患者 {} 药房 {} 获取token失败，跳过该分组", patientName, fyyf);
                    continue;
                }
                List<InPatientDispenseDetailBindScatteredVo> enriched = handleTracCodgStoreForInpatient(groupDetails, token);
                int assigned = assignTraceCodeToEmptyRecords(unSyncedData, enriched, patientName);
                totalAssigned += assigned;
                boolean confirmed = confirmDispenseData(enriched, token);
                if (!confirmed) {
                    log.warn("[XMZY] 患者 {} 药房 {} 确认拆零失败", patientName, fyyf);
                }
            }
            if (totalAssigned > 0) {
                nhsa3505Service.updateBatchById(unSyncedData);
            }
            return true;
        } catch (Exception e) {
            log.error("[XMZY] 患者 {} 追溯码赋值与确认异常", patientName, e);
            return false;
        }
    }

    /**
     * 将追溯码信息写回 3505 未同步记录
     */
    private int assignTraceCodeToEmptyRecords(List<Nhsa3505> unSyncedData,
                                              List<InPatientDispenseDetailBindScatteredVo> enriched,
                                              String patientName) {
        int assigned = 0;
        Map<String, InPatientDispenseDetailBindScatteredVo> byBchno = enriched.stream()
                .filter(d -> StringUtils.isNotBlank(d.getFixmedinsBchno()))
                .collect(Collectors.toMap(InPatientDispenseDetailBindScatteredVo::getFixmedinsBchno, Function.identity(), (a, b) -> b));
        for (Nhsa3505 n : unSyncedData) {
            try {
                String bchno = n.getFixmedinsBchno();
                if (StringUtils.isNotBlank(bchno) && byBchno.containsKey(bchno)) {
                    InPatientDispenseDetailBindScatteredVo d = byBchno.get(bchno);
                    if (d.getDrugTracCodgs() != null && !d.getDrugTracCodgs().isEmpty()) {
                        n.setDrugTracInfo(String.join(",", d.getDrugTracCodgs()));
                        n.setTrdnFlag(d.getTrdnFlag());
                        n.setInvCnt(d.getCurrNum());
                        n.setRemark("当日有明细自动赋码上传");
                        assigned++;
                    } else {
                        n.setInvCnt(BigDecimal.ZERO);
                        n.setRemark("没有赋码");
                    }
                }
            } catch (Exception ex) {
                log.error("[XMZY] 患者 {} 赋码异常，记录ID:{}", patientName, n.getId(), ex);
            }
        }
        log.info("[XMZY] 追溯码赋值完成：{} 条", assigned);
        return assigned;
    }

    /**
     * 确认拆零信息
     */
    private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
        try {
            List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
                    .filter(item -> "1".equals(item.getTrdnFlag()))
                    .collect(Collectors.toList());
            if (confirmList.isEmpty()) {
                return true;
            }
            List<ConfirmDispDrugDataRequest> dataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo r : confirmList) {
                ConfirmDispDrugDataRequest req = new ConfirmDispDrugDataRequest();
                req.setDrugCode(r.getHisDrugCode());
                req.setDispCnt(r.getSelRetnCnt());
                req.setCfxh(r.getCfxh());
                req.setCfmxxh(r.getCfmxxh());
                dataList.add(req);
            }
            if (!dataList.isEmpty()) {
                ConfirmDispDrugRequest batch = new ConfirmDispDrugRequest();
                batch.setDataList(dataList);
                SaasHttpUtil.confirmDispDrug(token, batch);
            }
            return true;
        } catch (Exception e) {
            log.error("[XMZY] 确认拆零信息异常", e);
            return false;
        }
    }

    /**
     * 上传未同步数据
     */
    private boolean processUnSyncedDataUpload(List<Nhsa3505> unSyncedData, InpatientSettlementVo patient) {
        try {
            List<Nhsa3505> filtered = unSyncedData.stream()
                    .filter(x -> StringUtils.isNotBlank(x.getDrugTracInfo()))
                    .collect(Collectors.toList());
            ApiResult<String> result = nhsa3505Service.processUpload(filtered, false);
            if (result.getCode() != 200) {
                log.error("[XMZY] 上传3505失败: {}", result.getMsg());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("[XMZY] 上传3505异常", e);
            return false;
        }
    }

    /**
     * 依据 fyyf 获取 SaaS Token（带缓存）
     */
    private String getTokenByFyyf(String fyyf) {
        try {
            SyncAccountEnum accountEnum = SyncAccountEnum.findByFyyf(fyyf);
            if (accountEnum == null) {
                log.error("[XMZY] 未找到 fyyf 对应账号配置: {}", fyyf);
                return null;
            }
            AccessTokenReponse cached = tokenCacheService.getTokenFromCache(accountEnum.getUsername());
            if (cached != null && cached.getReturnCode() == 0 && StrUtil.isNotBlank(cached.getAuthorization())) {
                return cached.getAuthorization();
            }
            AccessTokenReponse newToken = SaasHttpUtil.getAccessToken(accountEnum.getUsername(), accountEnum.getPassword());
            if (newToken == null || newToken.getReturnCode() != 0 || StrUtil.isBlank(newToken.getAuthorization())) {
                log.error("[XMZY] 获取token失败，账号: {}", accountEnum.getUsername());
                return null;
            }
            tokenCacheService.cacheToken(accountEnum.getUsername(), newToken);
            return newToken.getAuthorization();
        } catch (Exception e) {
            log.error("[XMZY] 获取token异常", e);
            return null;
        }
    }

    /**
     * 处理住院药品的追溯码库存信息
     */
    private List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            List<QueryTracDrugRequest> queryList = extendedList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());
            if (queryList.isEmpty()) {
                return extendedList;
            }
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryList);
            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse q = queryTracDrugMap.get(item.getCfmxxh());
                    item.setTrdnFlag(q.getIsTrac());
                    item.setHisConRatio(String.valueOf(q.getConRatio()));
                    if ("1".equals(q.getIsTrac())) {
                        tracDataList.add(GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build());
                    }
                }
            }
            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream().collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse t = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(t.getDrugCode());
                    item.setDrugTracCodgs(t.getDrugTracCodgs());
                    item.setDispCnt(t.getDispCnt());
                    item.setCurrNum(t.getCurrNum());
                    item.setTracCodgStore(t);
                }
            }
        } catch (Exception e) {
            log.error("[XMZY] 处理住院药品追溯码库存异常", e);
        }
        return extendedList;
    }

    /**
     * 判断费用发生时间是否在指定范围内
     */
    private boolean isWithinDateRange(LocalDateTime feeTime, String startDate, String endDate) {
        if (feeTime == null) return false;
        LocalDate feeDate = feeTime.toLocalDate();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        return !feeDate.isBefore(start) && !feeDate.isAfter(end);
    }

    /**
     * 查询医保5204费用明细（仅药品）
     */
    private List<Fsi5204Response> queryFeeDetails(InpatientSettlementVo patient) {
        try {
            com.zsm.model.nhsa.request.fsi5204.Selinfo5204 selinfo5204 = new com.zsm.model.nhsa.request.fsi5204.Selinfo5204();
            selinfo5204.setPsn_no(patient.getPsnNo());
            selinfo5204.setMdtrt_id(patient.getMdtrtSn());
            NhsaCityResponse<List<Fsi5204Response>> response = nhsaRetryUtil.executeWithRetry(
                    com.zsm.constant.NhsaAccountConstant.getNhsaAccount(),
                    currentSignNo -> NhsaHttpUtil.fsi5204(currentSignNo, selinfo5204, com.zsm.constant.NhsaAccountConstant.getNhsaAccount())
            );
            return response.getBody().getOutput().stream()
                    .filter(item -> "09".equals(item.getMedChrgitmType()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[XMZY] 查询5204费用明细异常", e);
            return Collections.emptyList();
        }
    }
}
