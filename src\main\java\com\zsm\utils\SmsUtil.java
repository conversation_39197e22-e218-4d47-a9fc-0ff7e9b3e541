package com.zsm.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 短信发送工具类
 * 提供调用内网服务发送短信的基础设施
 * 
 * <AUTHOR>
 * @date 2025/08/06
 */
@Slf4j
@Component
public class SmsUtil {

    /**
     * 短信服务接口地址
     */
    @Value("${shortmessage.url}")
    private String shortMessageUrl;

    /**
     * 默认连接超时时间（毫秒）
     */
    private static final int DEFAULT_TIMEOUT = 60000;

    /**
     * 日期时间格式
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 发送短信核心方法
     * 
     * @param phone      收信人电话号码
     * @param message    短信内容
     * @param datetime   发送时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param messageUrl 短信服务接口地址
     * @return 发送结果字符串
     */
    public static String sendMessage(String phone, String message, String datetime, String messageUrl) {
        log.info("开始发送短信 - 手机号: {}, 内容: {}, 发送时间: {}", phone, message, datetime);
        
        if (StrUtil.isBlank(phone)) {
            log.error("短信发送失败：手机号不能为空");
            return "手机号不能为空";
        }
        
        if (StrUtil.isBlank(message)) {
            log.error("短信发送失败：短信内容不能为空");
            return "短信内容不能为空";
        }
        
        if (StrUtil.isBlank(messageUrl)) {
            log.error("短信发送失败：短信服务接口地址未配置");
            return "短信服务接口地址未配置";
        }

        HttpURLConnection conn = null;
        OutputStream os = null;
        InputStream is = null;
        
        try {
            // 建立连接
            URI wsUri = URI.create(messageUrl);
            URL wsUrl = wsUri.toURL();
            conn = (HttpURLConnection) wsUrl.openConnection();
            
            // 设置连接参数
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(DEFAULT_TIMEOUT);
            conn.setReadTimeout(DEFAULT_TIMEOUT);
            conn.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
            
            // 构建SOAP请求体
            String xmlstr = buildSoapRequestXml(phone, message, datetime);
            log.debug("发送SOAP请求: {}", xmlstr);
            
            // 发送请求
            os = conn.getOutputStream();
            os.write(xmlstr.getBytes(StandardCharsets.UTF_8));
            os.flush();
            
            // 检查HTTP响应状态
            int responseCode = conn.getResponseCode();
            log.debug("HTTP响应状态码: {}", responseCode);
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("短信接口HTTP请求失败，状态码: {}", responseCode);
                return "短信接口HTTP请求失败，状态码: " + responseCode;
            }
            
            // 读取响应
            is = conn.getInputStream();
            byte[] buffer = new byte[10240];
            int len;
            StringBuilder response = new StringBuilder();
            while ((len = is.read(buffer)) != -1) {
                String responseChunk = new String(buffer, 0, len, StandardCharsets.UTF_8);
                response.append(responseChunk);
            }
            
            String responseBody = response.toString();
            log.debug("短信接口原始响应: {}", responseBody);
            
            // 解析响应XML
            String result = parseSoapResponse(responseBody);
            log.info("短信发送完成 - 手机号: {}, 结果: {}", phone, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("短信接口调用异常 - 手机号: {}, 异常信息: {}", phone, e.getMessage(), e);
            return "连接异常: " + e.getMessage();
        } finally {
            // 关闭资源
            if (is != null) {
                try {
                    is.close();
                } catch (Exception e) {
                    log.warn("关闭输入流异常: {}", e.getMessage());
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (Exception e) {
                    log.warn("关闭输出流异常: {}", e.getMessage());
                }
            }
            if (conn != null) {
                try {
                    conn.disconnect();
                } catch (Exception e) {
                    log.warn("断开连接异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 封装的短信发送方法（使用当前时间）
     * 
     * @param phone   收信人电话号码
     * @param message 短信内容
     * @return 发送结果字符串
     */
    public String sendSms(String phone, String message) {
        log.info("发送短信入参：手机号phone:{}, 短信内容message:{}", phone, message);
        String currentTime = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        return sendMessage(phone, message, currentTime, shortMessageUrl);
    }



    /**
     * 构建SOAP请求XML
     * 
     * @param phone    手机号
     * @param message  短信内容
     * @param datetime 发送时间
     * @return SOAP请求XML字符串
     */
    private static String buildSoapRequestXml(String phone, String message, String datetime) {
        StringBuilder xmlBuilder = new StringBuilder();
        xmlBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" ")
                .append("xmlns:thx=\"http://thxrmyy.org/\">\r\n")
                .append("   <soapenv:Header/>\r\n")
                .append("   <soapenv:Body>\r\n")
                .append("      <thx:addsmsOther>\r\n")
                .append("         <thx:destaddr>").append(escapeXml(phone)).append("</thx:destaddr>\r\n")
                .append("         <thx:messagecontent>").append(escapeXml(message)).append("</thx:messagecontent>\r\n")
                .append("         <thx:requesttime>").append(escapeXml(datetime)).append("</thx:requesttime>\r\n")
                .append("         <thx:zyhmmzhm></thx:zyhmmzhm>\r\n")
                .append("      </thx:addsmsOther>\r\n")
                .append("   </soapenv:Body>\r\n")
                .append("</soapenv:Envelope>");
        
        return xmlBuilder.toString();
    }

    /**
     * 解析SOAP响应
     * 
     * @param soapResponse SOAP响应XML
     * @return 解析后的结果字符串
     */
    private static String parseSoapResponse(String soapResponse) {
        try {
            if (StrUtil.isBlank(soapResponse)) {
                return "响应内容为空";
            }

            // 解析XML响应
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(soapResponse.getBytes(StandardCharsets.UTF_8)));

            // 查找响应结果节点
            // 根据SOAP响应结构查找结果节点
            NodeList bodyNodes = document.getElementsByTagName("soap:Body");
            if (bodyNodes.getLength() == 0) {
                bodyNodes = document.getElementsByTagName("soapenv:Body");
            }
            if (bodyNodes.getLength() == 0) {
                bodyNodes = document.getElementsByTagNameNS("*", "Body");
            }

            if (bodyNodes.getLength() > 0) {
                Element bodyElement = (Element) bodyNodes.item(0);
                
                // 查找addsmsOtherResponse节点
                NodeList responseNodes = bodyElement.getElementsByTagNameNS("*", "addsmsOtherResponse");
                if (responseNodes.getLength() > 0) {
                    Element responseElement = (Element) responseNodes.item(0);
                    
                    // 查找addsmsOtherResult节点
                    NodeList resultNodes = responseElement.getElementsByTagNameNS("*", "addsmsOtherResult");
                    if (resultNodes.getLength() > 0) {
                        String result = resultNodes.item(0).getTextContent();
                        return StrUtil.isNotBlank(result) ? result.trim() : "无返回结果";
                    }
                }
            }

            // 如果按标准SOAP结构解析失败，尝试通用解析
            Element root = document.getDocumentElement();
            NodeList allElements = root.getElementsByTagName("*");
            
            // 查找包含结果的节点（通常是最后一个非空文本节点）
            for (int i = allElements.getLength() - 1; i >= 0; i--) {
                Element element = (Element) allElements.item(i);
                String textContent = element.getTextContent();
                if (StrUtil.isNotBlank(textContent) && element.getChildNodes().getLength() == 1) {
                    return textContent.trim();
                }
            }

            return "无法解析响应结果";

        } catch (Exception e) {
            log.error("解析SOAP响应失败: {}", e.getMessage(), e);
            return "响应解析失败: " + e.getMessage();
        }
    }

    /**
     * XML转义
     * 
     * @param text 原始文本
     * @return 转义后的文本
     */
    private static String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }


}