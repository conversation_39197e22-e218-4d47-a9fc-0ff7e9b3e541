package com.zsm;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Spring Boot 应用启动类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EnableScheduling
@SpringBootApplication(exclude = {
    org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration.class
})
@MapperScan("com.zsm.mapper")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    /**
     * 应用启动完成后的监听器
     * 打印本机IP、端口和Swagger访问地址
     */
    @Component
    public static class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

        @Override
        public void onApplicationEvent(ApplicationReadyEvent event) {
            Environment env = event.getApplicationContext().getEnvironment();
            
            try {
                // 获取本机IP地址
                String hostAddress = InetAddress.getLocalHost().getHostAddress();
                
                // 获取端口号
                String port = env.getProperty("server.port", "8080");
                
                // 获取上下文路径
                String contextPath = env.getProperty("server.servlet.context-path", "");
                
                // 应用名称
                String appName = env.getProperty("spring.application.name", "应用");
                
                System.out.println("----------------------------------------------------------");
                System.out.println("\t" + appName + " 启动成功！");
                System.out.println("\t本机IP地址: \t\t" + hostAddress + ":" + port);
                System.out.println("\t本地访问地址: \thttp://localhost:" + port + contextPath);
                System.out.println("\t外部访问地址: \thttp://" + hostAddress + ":" + port + contextPath);
                System.out.println("\tSwagger文档: \thttp://localhost:" + port + contextPath + "/swagger-ui.html");
                System.out.println("\tSwagger文档: \thttp://" + hostAddress + ":" + port + contextPath + "/swagger-ui.html");
                System.out.println("\tAPI文档: \t\thttp://localhost:" + port + contextPath + "/v3/api-docs");
                System.out.println("----------------------------------------------------------");
                
            } catch (UnknownHostException e) {
                System.err.println("获取本机IP地址失败: " + e.getMessage());
            }
        }
    }
} 