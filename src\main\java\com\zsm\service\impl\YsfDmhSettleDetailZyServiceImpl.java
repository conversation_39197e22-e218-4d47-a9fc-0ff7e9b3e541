package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhSettleDetailZy;
import com.zsm.mapper.YsfDmhSettleDetailZyMapper;
import com.zsm.service.YsfDmhSettleDetailZyService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhSettleDetailZyServiceImpl extends ServiceImpl<YsfDmhSettleDetailZyMapper, YsfDmhSettleDetailZy> implements YsfDmhSettleDetailZyService {
}


