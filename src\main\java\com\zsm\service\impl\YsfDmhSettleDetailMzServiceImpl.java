package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhSettleDetailMz;
import com.zsm.mapper.YsfDmhSettleDetailMzMapper;
import com.zsm.service.YsfDmhSettleDetailMzService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhSettleDetailMzServiceImpl extends ServiceImpl<YsfDmhSettleDetailMzMapper, YsfDmhSettleDetailMz> implements YsfDmhSettleDetailMzService {
}


