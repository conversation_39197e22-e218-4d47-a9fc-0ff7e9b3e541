## 性能优化任务清单（可勾选）

说明：每条包含【问题】【建议/最佳实践】【代码位置】。按模块分组，完成后请勾选对应项。

---

### 一、数据库查询与索引

- [ ] 精简字段：Mapper 中存在 `SELECT *`
  - 问题：拉取全部列，放大 IO 与回表成本，影响缓存命中与网络传输。
  - 建议/最佳实践：改为“仅选所需列”；对热点查询做覆盖索引（包含查询列和排序列）。
  - 代码位置：
    - `src/main/resources/mapper/Nhsa3505Mapper.xml`（第 6、15、21 行均有 `select *`）

- [ ] ORDER BY 排序可能未命中索引
  - 问题：`ORDER BY ... DESC` 在大表上易触发 filesort 和临时表。
  - 建议/最佳实践：为排序字段建立合适索引；如带 WHERE 条件，建立联合索引（前缀顺序与查询一致）。
  - 代码位置：
    - `src/main/resources/mapper/Nhsa3505CntCountMapper.xml:174`（`order by count_date desc`）
    - `src/main/resources/mapper/YsfStoTcTaskMapper.xml:31`（`ORDER BY id_task DESC`）
    - `src/main/resources/mapper/YsfStoTcTaskSubMapper.xml:24`（`ORDER BY id_sub DESC`）
    - `src/main/resources/mapper/YsfStoTcStatusMapper.xml:35`（`ORDER BY id DESC`）
    - `src/main/resources/mapper/YsfStoDpsSubMapper.xml:41`（`ORDER BY sub.id DESC`）
    - `src/main/resources/mapper/Nhsa3505Mapper.xml:25`（`order by id desc` + `limit 1`）

- [ ] 在索引列上使用函数/表达式，破坏索引
  - 问题：`date(create_time)`、`LENGTH(...)` 等表达式参与查询/统计，可能导致全表扫描。
  - 建议/最佳实践：
    - 用范围过滤替代函数：`create_time BETWEEN ? AND ?`；
    - 统计类需求考虑预聚合/物化表；
    - 必须使用函数时考虑生成列（computed/virtual）+ 索引。
  - 代码位置（示例）：
    - `src/main/java/com/zsm/superset/Nhsa3505YmfUserCountAsync.java` 多处使用 `qw.select("date(create_time)")`、`selectObjs` 聚合
    - `src/main/java/com/zsm/superset/Nhsa3505CntCountAsync.java` 多处 `date(create_time)`、`sum(...)` 统计

- [ ] 大列表查询缺少稳定分页与限制
  - 问题：部分统计/导出场景可能直接 `selectList/selectObjs` 全量拉取。
  - 建议/最佳实践：所有列表统一使用分页（`Page/IPage`），或游标/流式处理；导出使用分段拉取。
  - 代码位置（自查清单）：
    - `src/main/java/com/zsm/superset/Nhsa3505YmfUserCountAsync.java`（多处 `selectList/selectObjs`）
    - `src/main/java/com/zsm/superset/Nhsa3505CntCountAsync.java`（多处 `selectList/selectObjs`）

- [ ] 索引建议（按使用痕迹汇总）
  - 建议/最佳实践：
    - 表 `nhsa3505`：为常用过滤/去重字段建立索引（如 `cfmxxh`、`create_time`），排序字段如 `id` 保证为主键/索引；
    - 表 `ysf_sto_tc`：`drugtracinfo`（追溯码）用于 `IN`/去重，建议唯一索引或高选择性普通索引；
    - 表 `ysf_sto_tc_task`：`id_task` 排序/查询字段加索引；
    - 表 `ysf_sto_tc_status`：主键/时间戳字段用于查询/排序需索引；
    - 复核所有 `ORDER BY` 字段是否有对应（联合）索引。

---

### 二、循环内外部调用 / DB 访问

- [ ] 循环内外部 HTTP 调用（确认/查询）
  - 问题：N 次循环内逐条请求，吞吐低且易被限流；失败重试放大耗时。
  - 建议/最佳实践：
    - 支持批量端点（50/100 条一批）；
    - 若对方不支持，使用“有界并发”+ 限流（线程池 + 信号量 + 指数退避），并做批失败回退与幂等；
    - 统一重试策略，避免在业务线程中 `sleep`。
  - 代码位置：
    - `src/main/java/com/zsm/service/impl/DispensingServiceImpl.java:653-659`（逐条确认 `confirmDispDrug`）
    - `src/main/java/com/zsm/service/HangChuangService.java` 多处外呼（如 115-205、1390-1393、1836-1840 行附近）

- [ ] 循环内 DB 逐条查询/写入
  - 问题：在 for 循环中多次 `selectOne/selectCount` 或单条写入；
  - 建议/最佳实践：
    - 读：改为一次性 `IN` 批量查询（已有部分实现，需统一到所有路径）；
    - 写：`saveBatch/updateBatchById` 分片批量提交（500~1000/批），避免长事务和大事务。
  - 代码位置：
    - `src/main/java/com/zsm/service/YsfService.java:1250-1268`（按 1000 批次 `IN` 查询示例，可复用到其他路径）
    - `src/main/java/com/zsm/service/impl/DispensingServiceImpl.java`（`saveBatch`/`updateBatchById` 已使用，排查剩余逐条写）

---

### 三、异步与线程池

- [ ] 缺少统一异步线程池与 `@EnableAsync`
  - 问题：存在 `@Async` 使用，但未发现统一 `ThreadPoolTaskExecutor` 配置；可能出现线程不可控、上下文丢失或异步无效。
  - 建议/最佳实践：
    - 新增 `AsyncConfig`：为不同业务配置独立线程池（核心/最大线程、队列、存活、命名、MDC 传播、拒绝策略）；
    - 启用 `@EnableAsync`；在方法上指定 `@Async("beanName")`；
    - 暴露线程池运行指标。
  - 代码位置（含 `@Async` 热点）：
    - `src/main/java/com/zsm/service/impl/Nhsa3505ServiceImpl.java:82, 256, 379`
    - `src/main/java/com/zsm/service/impl/Nhsa3506ServiceImpl.java:57`
    - `src/main/java/com/zsm/service/FuYangRenYiAnsycService.java:26, 82`

- [ ] `synchronized` 临界区过大
  - 问题：异步统计类使用 `synchronized` 使热点串行化，限制吞吐。
  - 建议/最佳实践：
    - 使用基于 Key 的细粒度并发控制（如 `ConcurrentHashMap<key, Lock>` 或基于 Redis 的短期锁）；
    - 限定锁作用域到必要的数据行。
  - 代码位置：
    - `src/main/java/com/zsm/superset/Nhsa3505YmfUserCountAsync.java:251`
    - `src/main/java/com/zsm/superset/Nhsa3505CntCountAsync.java:140`

---

### 四、事务边界与锁

- [ ] 事务包裹外部调用/大批处理
  - 问题：事务中执行 HTTP 或长时逻辑导致长事务、锁占用、死锁风险。
  - 建议/最佳实践：
    - 事务内仅保留必要 DB 操作；
    - 批处理分片 + 每片独立事务；
    - 对需要新事务的写操作使用 `REQUIRES_NEW`，避免顶层长事务。
  - 代码位置：
    - `src/main/java/com/zsm/service/impl/DispensingServiceImpl.java:586, 608, 761`
    - `src/main/java/com/zsm/service/impl/DataPersistenceServiceImpl.java:59, 679`
    - `src/main/java/com/zsm/service/YsfService.java:849`

---

### 五、Redis 使用

- [ ] 批量操作缺少 pipeline/mget
  - 问题：多 key 操作逐条执行，RTT 往返成本高。
  - 建议/最佳实践：
    - 批读用 `mget` 或 pipeline；
    - 热点键增加本地缓存（Caffeine）+ 合理 TTL；
    - 规范序列化（已使用 Jackson，可继续统一）。
  - 代码位置（封装类与调用点自查）：
    - `src/main/java/com/zsm/utils/RedisUtil.java`（`opsForValue` 多处：37, 39, 55, 71, 118 行）
    - `src/main/java/com/zsm/service/TokenCacheService.java`、`src/main/java/com/zsm/service/SignNoCacheService.java`（按访问模式评估是否需要批接口）

---

### 六、重试与退避策略

- [ ] 使用 `Thread.sleep` 阻塞工作线程
  - 问题：阻塞线程降低吞吐，影响线程池利用率。
  - 建议/最佳实践：
    - 使用 Resilience4j Retry + Exponential Backoff（非阻塞/可观测）；
    - 至少缩短 sleep、让重试委派到独立受控线程池；
    - 区分 4xx/5xx：4xx 不重试或快速失败。
  - 代码位置：
    - `src/main/java/com/zsm/utils/SaasHttpUtil.java:229`（递增延迟 `Thread.sleep`）
    - `src/main/java/com/zsm/service/HangChuangService.java:1115`

- [ ] 重试与超时统一规范
  - 建议/最佳实践：
    - 统一连接/读写超时、最大重试次数、退避策略与熔断阈值；
    - 在日志中打印 `requestId`、重试次数、耗时分布。
  - 代码位置：
    - `src/main/java/com/zsm/utils/NhsaRetryUtil.java`、`src/main/java/com/zsm/utils/SaasHttpUtil.java`、`src/main/java/com/zsm/utils/NhsaHttpUtil.java`

---

### 七、HTTP 连接与资源复用

- [ ] 连接池与复用策略
  - 问题：默认 HTTP 客户端配置在高并发下可能不足；
  - 建议/最佳实践：
    - 统一使用可复用连接的客户端（OkHttp/Apache HC）并集中配置连接池；
    - 统一拦截器做鉴权注入、日志脱敏、重试与 metrics；
    - 所有外部调用统一超时（连接/读/写），分级设置。
  - 代码位置（外部调用集中处）：
    - `src/main/java/com/zsm/utils/SaasHttpUtil.java`
    - `src/main/java/com/zsm/utils/NhsaHttpUtil.java`
    - `src/main/java/com/zsm/utils/SoapUtil.java`
    - `src/main/java/com/zsm/service/HangChuangService.java`

---

### 八、批处理规模与内存

- [ ] 大批处理分片与常量统一
  - 问题：不同类批大小不一致（50、1000 等），不利于统一调优；超大批次易撑爆内存/事务。
  - 建议/最佳实践：
    - 统一批处理常量（如：读 1000、写 500）；
    - 分批提交 + 间隔让出线程；
    - JDBC 批量参数：开启 `rewriteBatchedStatements`，控制批写大小。
  - 代码位置：
    - `src/main/java/com/zsm/service/impl/DispensingServiceImpl.java:63`（`BATCH_SIZE = 50`）
    - `src/main/java/com/zsm/service/impl/DataPersistenceServiceImpl.java:56`（`BATCH_SIZE = 1000`）
    - `src/main/java/com/zsm/service/HangChuangService.java:1092-1113`（批量上传药品数据分片示例）

---

### 九、监控与可观测性

- [ ] SQL 慢查询与热点跟踪
  - 建议/最佳实践：
    - 打开慢查询日志与阈值报警；对热点 SQL 加入模板化日志（带耗时和行数）。

- [ ] 线程池/外部 API 指标
  - 建议/最佳实践：
    - 导出线程池活跃数、队列长度、拒绝次数；
    - 外部 API 的 QPS/RT/错误率/重试次数；
    - 结合现有 `TraceIdFilter` 做全链路关联日志。

---

### 十、日志与脱敏

- [ ] 循环内大量日志输出
  - 问题：放大 I/O，污染日志，影响 GC。
  - 建议/最佳实践：减少循环内日志，使用聚合统计日志；敏感信息脱敏。
  - 代码位置：
    - 重点排查：`src/main/java/com/zsm/service/impl/DispensingServiceImpl.java`、`src/main/java/com/zsm/service/HangChuangService.java`、`src/main/java/com/zsm/service/YsfService.java`

---

## 附：落地优先级建议

- P0（尽快）：`SELECT *` 精简与索引补齐；循环内外呼改批/限并发；统一异步线程池；事务边界收缩；重试去 `sleep`。
- P1：统计类函数改范围/预聚合；Redis 批操作与热点本地缓存；HTTP 客户端统一。
- P2：监控指标体系与日志降噪；批处理常量统一与参数调优。

---

最后更新人：gpt5
最后更新时间：2025年8月8日 11:02:09
