package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhReturnPatientMz;
import com.zsm.mapper.YsfDmhReturnPatientMzMapper;
import com.zsm.service.YsfDmhReturnPatientMzService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhReturnPatientMzServiceImpl extends ServiceImpl<YsfDmhReturnPatientMzMapper, YsfDmhReturnPatientMz> implements YsfDmhReturnPatientMzService {
}


