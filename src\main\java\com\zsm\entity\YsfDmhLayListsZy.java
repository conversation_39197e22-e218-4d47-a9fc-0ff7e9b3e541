package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 住院-发药记录列表视图 V_YSF_DMH_LAY_LISTS_ZY
 */
@Data
@TableName("V_YSF_DMH_LAY_LISTS_ZY")
@Schema(description = "住院-发药记录列表视图")
public class YsfDmhLayListsZy implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("RECORD_ID")
    @Schema(description = "发药记录id（发药/退药单号）")
    private String recordId;

    @TableField("PAT_WARD_ID")
    @Schema(description = "病区id")
    private String patWardId;

    @TableField("PAT_WARD_NAME")
    @Schema(description = "病区名称")
    private String patWardName;

    @TableField("FG_DPS")
    @Schema(description = "发药单标记 0发药 1退药")
    private String fgDps;

    @TableField("SEND_FLAG")
    @Schema(description = "发药标志 0待发药 1已发药 7部分退药 8全部退药 9作废")
    private String sendFlag;

    @TableField("RECORD_TIME")
    @Schema(description = "记录时间")
    private String recordTime;

    @TableField("PHAR_NAME")
    @Schema(description = "药师姓名")
    private String pharName;

    @TableField("SUMMARY")
    @Schema(description = "记录摘要，如涉及患者数/药品数")
    private String summary;
}


