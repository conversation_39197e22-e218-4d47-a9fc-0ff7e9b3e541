package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 住院-结算记录视图 V_YSF_DMH_SETTLE_ZY
 */
@Data
@TableName("V_YSF_DMH_SETTLE_ZY")
@Schema(description = "住院-结算记录视图")
public class YsfDmhSettleZy implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("PAT_IN_HOS_ID")
    @Schema(description = "住院id（住院唯一标识）")
    private String patInHosId;

    @TableField("PATIENT_ID")
    @Schema(description = "患者ID")
    private String patientId;

    @TableField("PATIENT_NAME")
    @Schema(description = "患者名称")
    private String patientName;

    @TableField("SETL_STATUS")
    @Schema(description = "结算状态")
    private String setlStatus;

    @TableField("SETL_TIME")
    @Schema(description = "结算时间")
    private String setlTime;

    @TableField("SETL_UPLOAD_TIME")
    @Schema(description = "结算上传时间")
    private String setlUploadTime;

    @TableField("MDTRT_ID")
    @Schema(description = "就诊ID（医保）")
    private String mdtrtId;

    @TableField("SETL_ID")
    @Schema(description = "结算ID")
    private String setlId;

    @TableField("MDTRT_SETL_TYPE")
    @Schema(description = "就诊结算类型 1-医保结算 2-自费结算")
    private String mdtrtSetlType;

    @TableField("PSN_NO")
    @Schema(description = "人员编号（医保人员编号）")
    private String psnNo;
}


