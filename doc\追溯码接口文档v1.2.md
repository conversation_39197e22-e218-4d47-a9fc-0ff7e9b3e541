# 云速付药械数字化云平台 医疗机构侧接口 (V1.2)

## 目录

* **第1章 范围**
* **第2章 规范性引用文件**
* **第3章 接口报文格式**
    * 3.1 交易状态码说明
    * 3.2 重点说明
    * 3.3 接口调用说明
* **第4章 接口说明**
    * 4.1 基础接口
        * 4.1.1 JC-001 获取授权接口 (必要)
            * ******* 接口说明
            * ******* 重点说明
            * ******* 接口地址
            * ******* 输入
            * ******* 输出
            * 4.1.1.6 请求示例
    * 4.2 对码接口
        * 4.2.1 DMH-001 HIS 药品数据上传接口 (必要)
            * 4.2.1.1 接口说明
            * 4.2.1.2 接口地址
            * 4.2.1.3 输入
            * 4.2.1.4 输出
            * 4.2.1.5 请求示例
        * 4.2.2 DMH-003 HIS 配送企业数据上传接口 (必要)
            * 4.2.2.1 接口说明
            * 4.2.2.2 接口地址
            * 4.2.2.3 输入
            * 4.2.2.4 输出
            * 4.2.2.5 请求示例
    * 4.3 采购接口
        * 4.3.1 CGH-003 收货信息查询接口 (云速付提供, 必要)
            * ******* 接口说明
            * ******* 重点说明
            * ******* 接口地址
            * ******* 输入
            * 4.3.1.5 输出
            * 4.3.1.6 请求示例
* **第5章 字典枚举说明**
    * 5.1 返回编号状态枚举
* **第6章 门诊接口**
    * 6.1 门诊发药处方接口或视图 (his 提供必须)
        * 6.1.1.1 接口说明
        * 6.1.1.2 接口地址
        * 6.1.1.3 输入
        * 6.1.1.4 输出
    * 6.2 门诊退药接口或视图 (his 提供必须)
        * 6.2.1.1 接口说明
        * 6.1.1.2 接口地址
        * 6.2.1.3 输入
        * 6.2.1.4 输出
* **第7章 住院追溯码采集**
    * 7.1 领药单明细视图
        * 7.1.1.1 接口说明
        * 7.1.1.2 接口地址
        * 7.1.1.3 输入
        * 7.1.1.4 输出
    * 7.2 住院药房领药记录列表接口或视图 (his 提供) 没法提供发药打印凭条的话
        * 7.2.1.1 接口说明
        * 7.2.1.2 输入
        * 7.2.1.3 输出
    * 7.3 出院带药接口或视图 (his 提供)
        * 7.3.1.1 接口说明
        * 7.3.1.2 接口地址
        * 7.3.1.3 输入
        * 7.3.1.4 输出
    * 7.4 出院结算采集
        * 7.4.1 出院结算用户列表接口或视图 (his 提供必须)
            * 7.4.1.1 接口说明
            * 7.4.1.2 接口地址
            * 7.4.1.3 输入
            * 7.4.1.4 输出
        * 7.4.2 出院结算明细列表接口或者视图 (his 提供必须)
            * 7.4.2.1 接口说明
            * 7.4.2.2 接口地址
            * 7.4.2.3 输入
            * 7.4.2.4 输出
* **第8章 调拨库存接口**
    * 8.1 药品出入库接口或者视图 (his 提供)
        * 8.1.1.1 接口说明
        * 8.1.1.2 接口地址
        * 8.1.1.3 输入
        * 8.1.1.4 输出
    * 8.2 药品库存接口或视图 (his 提供)
        * 8.2.1.1 接口说明
        * 8.2.1.2 接口地址
        * 8.2.1.3 输入
        * 8.2.1.4 输出
* **第9章 字典接口**
    * 9.1 科室字典接口或视图 (his 提供前期可以提供表格)
        * 9.1.1.1 接口说明
        * 9.1.1.2 接口地址
        * 9.1.1.3 输入
        * 9.1.1.4 输出

---

## 第1章 范围

本规范适用于云速付药械数字化云平台医疗机构侧接口说明。

## 第2章 规范性引用文件

下列文件对于本文件的应用是必不可少的。凡是注日期的引用文件,仅所注日期的版本适用于本文件。

凡是不注日期的引用文件,其最新版本(包括所有的修改单)适用于本文件。

## 第3章 接口报文格式

### 3.1 交易状态码说明

交易状态码 (returnCode) 规格如下:

| 序号 | returnCode 值 | 值说明 | 备注 |
|---|---|---|---|
| 1 | 0 | 成功 |  |

### 3.2 重点说明

*   调用交易时 INPUT、OUTPUT 节点应按照接口安全相关要求进行签名。
*   时间格式代码说明: yyyy (年, 4位)、MM (月, 2位)、dd (日, 2位)、HH (24小时制, 2位)、mm (分钟, 2位)、ss (秒, 2位)、SSS (毫秒, 3位)。
*   日期型的数据元(例如开始时间)格式为: yyyy-MM-dd HH:mm:ss; 日期型的数据元(例如开始日期)格式为: yyyy-MM-dd。
*   查询中输入开始结束时间, 格式为 yyyy-MM-dd, 时间范围默认开始于 00:00:00, 结束于 23:59:59。例如时间 2020-01-01~2020-01-02 则匹配时间 2020-01-01 00:00:00~2020-01-02 23:59:59 的数据。
*   报文中的输入/输出项的字符型串中的根节点和各个子节点一律小写。
*   类型为数值的参数, 如果为空, 必须传"0", 其他为空串 (""), TXT 文件中空值使用 "null"。
*   TXT 文件使用字符集为 UTF-8。
*   接口说明中声明的输入为输入报文中 INPUT 属性内容, 输出为输出报文中 OUTPUT 属性内容。除文件上传下载交易 (【9101】、【9102】) 外, 所有交易都应该有输入输出报文。文件上传下载交易对应文件以流式数据传输。
*   接口输入、输出数据元代码标识为"Y"的, 字典内容参照文章中字典表部分内容。
*   报文中 INPUT/OUTPUT (输入信息/输出信息) 要符合 JSON 格式的约定。
*   如果信息中出现的下列字符, 需要进行转义处理:
    1.  `"` 转义为 `\"`;
    2.  `\` 转义为 `\\`。

### 3.3 接口调用说明

生产环境接口前缀: `http://pipy.ysfcloud.com:30800/saas-prod/prod`
测试环境接口前缀: `http://pipy-test.ysfcloud.com:30800/saas-test/prod`

医疗机构进行接口联调时, 接口调用地址由"接口前缀" + "接口地址"组成, 如测试环境调用 "JC-001 获取授权"接口, 接口调用地址为:
`http://pipy-test.ysfcloud.com:30800/saas-test/prod/drug/interface/medins/accessToken`

## 第4章 接口说明

### 4.1 基础接口

#### 4.1.1 JC-001 获取授权接口 (必要)

##### ******* 接口说明

用于安全校验, 获取其他接口调用令牌, 后面接口请求时必须带上该接口返回的参数 "authorization", 接口调用频次为天, 令牌获取后有效期为当天, 即 00:00:00 至 23:59:59。

##### ******* 重点说明

获取到 authorization 后把参数放到接口的 header 中参数名为 Access-Token 下。

##### ******* 接口地址

接口地址: `/drug/interface/medins/accessToken`

##### ******* 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 1 | userAccount | 账号 | 字符型 | 30 | Y |  |
| 2 | userPassWord | 密码 | 字符型 | 100 | Y |  |

##### ******* 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | returnCode | 返回编号 | 数值型 | 4 | 状态码, 详见 5.1 |
| 2 | returnMsg | 返回信息 | 字符型 | 100 | 说明, 详见 5.1 |
| 3 | authorization | 访问凭证 | 字符型 | 300 | 当天有效 |
| 4 | userAccount | 账号 | 字符型 | 30 |  |
| 5 | nickName | 用户名称 | 字符型 | 30 |  |
| 6 | orgId | 机构编码 | 字符型 | 40 |  |
| 7 | orgName | 机构名称 | 字符型 | 200 |  |

##### 4.1.1.6 请求示例

**入参:**

```json
{
"userAccount": "***",
"userPassWord": "***"
}
```

**出参:**

```json
{
"returnCode": 0,
"returnMsg": "***",
"authorization": "***",
"userAccount": "***",
"nickName": "***",
"orgId": "***",
"orgName": "***"
}
```

### 4.2 对码接口

#### 4.2.1 DMH-001 HIS 药品数据上传接口 (必要)

##### 4.2.1.1 接口说明

用于 HIS 上传药品数据, 场景同"对码模块→药品本院目录→HIS 对码→导入"功能。可以 HIS 开视图, 云速付查询, 也可以直接调用云速付的接口。

##### 4.2.1.2 接口地址

接口地址: `/system/interface/medins/importDrug`

##### 4.2.1.3 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 1 | requestID | 请求全局交 互 ID | 字符型 | 100 | Y | 用于双方核查日 志 |
| 2 | dataList | 数组对象 | 数组 |  | Y |  |
|  | `├─hisDrugId` | HIS 唯一值 | 字符型 | 200 | Y | HIS 药品主键唯 一值 |
|  | `├─hisEnterpriseCode` | HIS 配送企 业编码 | 字符型 | 255 | N | HIS 系统企业编 码唯一值 |
|  | `├─hisEnterpriseName` | HIS 配送企 业名称 | 字符型 | 255 | N |  |
|  | `├─hisDrugName` | 药品名称 | 字符型 | 200 | N |  |
|  | `├─hisDrugCountryCode` | 国家药品代 码 | 字符型 | 50 | N |  |
|  | `├─ThisDrugCountryName` | 国家标准名 称 | 字符型 | 200 | N |  |
|  | `├─hisDrugSpec` | 规格 | 字符型 | 200 | N |  |
|  | `├─ThisPac` | 包装 | 字符型 | 255 | N |  |
|  | `├─ThisPackUnit` | 包装单位 | 字符型 | 50 | N |  |
|  | `├─hisDrugManufacturerCode` | 生产企业编 码 | 字符型 | 255 | N |  |
|  | `├─hisDrugManufacturerName` | 生产企业 | 字符型 | 200 | N |  |
|  | `├─hisPurchaseUnit` | 采购单位 | 字符型 | 50 | N |  |
|  | `├─hisPurchasePrice` | 采购价格 | Decimal | 18,4 | N |  |
|  | `├─hisDoseForm` | 剂型 | 字符型 | 200 | N |  |
|  | `├─hisApprovalNum` | 批准文号 | 字符型 | 100 | N |  |
|  | `├─his_dos_unit` | 最小剂量单 位 | 字符型 | 100 | N |  |
|  | `├─_his_pac_unit` | 最小包装单 位 | 字符型 | 100 | N |  |
|  | `├─ThisConRatio` | 转换比 | Decimal | 18,4 | N |  |
|  | `├─wholeQuantity` | 整件数量 | 数值型 | 8 | N |  |
|  | `├─ThisDiscRate` | 折扣率 | Decimal | 18,4 | N |  |
|  | `├─memo` | 备注 | 字符型 | 255 | N |  |
|  | `└─delFlag` | 是否删除 | 数值型 | 4 | N | 数据是否删除标 志, 默认 0 0:存在; 1:删除; |

##### 4.2.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | returnCode | 返回编号 | 数值型 | 4 | 状态码, 详见 5.1 |
| 2 | returnMsg | 返回信息 | 字符型 | 100 | 说明, 详见 5.1 |
| 3 | resultList |  | 对象 |  |  |
|  | `├─returnCode` | 返回编号 | 数值型 | 4 |  |
|  | `├─returnMsg` | 返回信息 | 字符型 | 100 |  |
|  | `└─hisDrugId` | HIS 唯一值 | 字符型 | 200 |  |

##### 4.2.1.5 请求示例

**入参:**

```json
{
"requestID": "***",
"dataList": [
{
"hisDrugId": "***",
"hisEnterpriseCode": "***",
"hisEnterpriseName": "***",
"hisDrugName":"***",
"hisDrugCountryCode":"***",
"hisDrugCountryName":"***",
"hisDrugSpec":"***",
"hisPac":"***",
"hisDrugManufacturerCode":"***",
"hisDrugManufacturerName":"***",
"hisPurchaseUnit":"***",
"hisPurchasePrice":"***",
"hisDoseForm":"***",
"hisApprovalNum":"***",
"his_dos_unit":"***",
"his_pac_unit":"***",
"hisConRatio":"***",
"wholeQuantity":"***",
"memo":"***",
"delFlag":"***"
}
]
}
```

**出参:**

```json
{
"returnCode": 0,
"returnMsg": "***",
"resultList": [
{
"returnCode": "***",
"returnMsg": "***",
"hisDrugId": "***"
}, {
"returnCode": "***",
"returnMsg": "***",
"hisDrugId": "***"
}
]
}
```

#### 4.2.2 DMH-003 HIS 配送企业数据上传接口 (必要)

##### 4.2.2.1 接口说明

用于 HIS 上传配送企业数据, 场景同"对码模块→配送企业对码→导入"功能。可以 HIS 开视图, 云速付查询, 也可以直接调用云速付的接口。

##### 4.2.2.2 接口地址

接口地址: `/system/interface/medins/importEntp`

##### 4.2.2.3 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 1 | requestID | 请求全局交 互 ID | 字符型 | 100 | Y | 用于双方核查日 志 |
| 2 | dataList | 数组对象 | 数组 |  | Y |  |
|  | `├─hisEnterpriseCode` | HIS 配送企业编 码 | 字符型 | 40 | Y | HIS 系统企业编 码唯一值 |
|  | `├─hisEnterpriseName` | HIS 配送企业名 称 | 字符型 | 200 | N |  |
|  | `├─hisEnterpriseUscc` | HIS 企业统一社 会信用代码 | 字符型 | 50 | N |  |
|  | `└─delFlag` | 是否删除 | 数值型 | 4 | N | 数据是否删除标 志, 默认 0 0:存在; 1:删除; |

##### 4.2.2.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | returnCode | 返回编号 | 数值型 | 4 | 状态码, 详见 5.1 |
| 2 | returnMsg | 返回信息 | 字符型 | 100 | 说明, 详见 5.1 |
| 3 | resultList |  | 对象 |  |  |
|  | `├─returnCode` | 返回编号 | 数值型 | 4 |  |
|  | `├─returnMsg` | 返回信息 | 字符型 | 100 |  |
|  | `└─hisEnterpriseCode` | HIS 配送企业编码 | 字符型 | 40 |  |

##### 4.2.2.5 请求示例

**入参:**

```json
{
"requestID": "***",
"dataList":[
{
"hisEnterpriseCode": "***",
"hisEnterpriseName": "***",
"hisEnterpriseUscc": "***",
"delFlag": "***"
}, {
"hisEnterpriseCode": "***",
"hisEnterpriseName": "***",
"hisEnterpriseUscc": "***",
"delFlag": "***"
}
]
}
```

**出参:**

```json
{
"returnCode": 0,
"returnMsg": "***",
"resultList": [
{
"returnCode": "***",
"returnMsg": "***",
"hisEnterpriseCode": "***"
}, {
"returnCode": "***",
"returnMsg": "***",
"hisEnterpriseCode": "***"
}
]
}
```

### 4.3 采购接口

#### 4.3.1 CGH-003 收货信息查询接口 (云速付提供, 必要)

##### ******* 接口说明

HIS 查询云速付平台的已收货的数据, 然后写入到 HIS 系统里面, 也就是 HIS 自己需要做一个入库单 制作的接口。

##### ******* 重点说明

获取的数据将以自增 ID (incNum) 升序排列。

##### ******* 接口地址

接口地址: `/purc/interface/medins/getShippingInformation`

##### ******* 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 1 | requestID | 请求全局交 互 ID | 字符型 | 100 | Y | 用于双方核查日志 |
| 2 | currentPageNumb er | 当前页 | 数值型 | 4 | Y |  |
| 3 | size | 每页条数 | 数值型 | 8 | Y |  |
| 4 | beginTime | 开始时间 | 日期型 | 10 | N | yyyy-MM-dd HH:mm:ss |
| 5 | endTime | 结束时间 | 日期型 | 10 | N | yyyy-MM-dd HH:mm:ss |
| 6 | incNum | 自增 ID | 数值型 | 8 | N | 自增获取起始序号。不传默认 0 |
| 7 | ordNos | 采购主单号 数组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 8 | ordDetlCodes | 采购细单号 数组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 9 | hisordNos | HIS 采购主单 号数组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 10 | hisUniqueKeys | HIS 采购细单 号数组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 11 | shpNos | 发货单号数 组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 12 | shpDet Nos | 发货明细单 号数组 | 数组 |  | N | 通过","分隔, 示例 a, b, c (最多 50 个) |
| 13 | shpStas | 状态 | 数值型 | 4 | N | 1:待收货; 2:拒绝收货; 3:已收货; |
| 14 | entpShpNos | 企业发货单 号数组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |
| 15 | entpCodes | 企业编号数 组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |
| 16 | invoCode | 发票代码 | 字符型 |  | N |  |
| 17 | invono | 发票号码 | 字符型 |  | N |  |
| 18 | prodNames | 药品名称数 组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |
| 19 | prodentpNames | 生产企业数 组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |
| 20 | pubonlnProdIDs | 省药品编码 数组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |
| 21 | hisDrugIds | HIS 药品编码 数组 | 数组 |  | N | 通过","隔开, 示例: a, b, c (最多 50 个) |

##### 4.3.1.5 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | returnCode | 返回编号 | 数值型 | 4 | 状态码, 详见 5.1 |
| 2 | returnMsg | 返回信息 | 字符型 | 100 | 说明, 详见 5.1 |
| 3 | totalPageCount | 总页数 | 数值型 | 8 |  |
| 4 | totalRecordCount | 总记录条数 | 数值型 | 8 |  |
| 5 | currentPageNumber | 当前页数 | 数值型 | 8 |  |
| 6 | resultList |  | 对象 |  |  |
|  | `├─ordNo` | 采购主单号 | 字符型 | 40 |  |
|  | `├─ordDetlCode` | 采购细单号 | 字符型 | 40 |  |
|  | `├─hisOrdNo` | HIS 采购主单号 | 字符型 | 40 |  |
|  | `├─hisUniqueKey` | HIS 采购细单号 | 字符型 | 40 |  |
|  | `├─ordDetlStas` | 采购细单状态 | 数值型 | 4 | 0:已保存 1:已提交 2:已阅 读 3:拒绝发货 4:部分发货 5:完成发货 6:已作废 |
|  | `├─sbmtTime` | 提交时间 | 日期型 | 20 |  |
|  | `├─stroomID` | 库房 ID | 字符型 | 40 |  |
|  | `├─stroomName` | 库房名称 | 日期型 | 20 |  |
|  | `├─medinsCode` | 医疗机构代码 | 字符型 | 40 |  |
|  | `├─medinsName` | 医疗机构名称 | 字符型 | 200 |  |
|  | `├─shpNo` | 发货主单号 | 字符型 | 40 |  |
|  | `├─shpDet No` | 发货细单号 | 字符型 | 40 |  |
|  | `├─hisDrugId` | HIS 药品编码 | 字符型 | 40 |  |
|  | `├─pubonlnProdID` | 省药品编码 | 字符型 | 40 |  |
|  | `├─ver` | 版本 | 数值型 | 4 |  |
|  | `├─drugUnino` | 国家药品编码 | 字符型 | 50 |  |
|  | `├─prodName` | 产品名称 | 字符型 | 200 |  |
|  | `├─dosform` | 剂型 | 字符型 | 200 |  |
|  | `├─spec` | 规格 | 字符型 | 200 |  |
|  | `├─pac` | 包装 | 字符型 | 50 |  |
|  | `├─pacmatl` | 包装材质 | 字符型 | 500 |  |
|  | `├─prodentpName` | 生产企业 | 字符型 | 200 |  |
|  | `├─itemname` | 项目名称 | 字符型 | 100 |  |
|  | `├─isCapacity` | 是否带量 | 数值型 | 4 | 0表示非带量, 1表示带量 |
|  | `├─pubonlnPric` | 挂网价格(元) | 浮点型 | 18 |  |
|  | `├─purcPrice` | 采购价格 | 浮点型 | 18, 4 |  |
|  | `└─discRate` | 扣点 | 浮点型 | 18,4 |  |

##### 4.3.1.6 请求示例

**入参:**

```json
{
"requestID": "***",
"currentPageNumber": "***",
"size": "***",
"beginTime": "***",
"endTime": "***",
"incNum": "***",
"ordNos": "***, ***",
"ordDetlCodes": "***, ***",
"hisOrdNos": "***, ***",
"hisUniqueKeys": "***, ***",
"shpNos": "***, ***",
"shpDet Nos": "***, ***",
"shpStas": "***",
"entpShpNos": "***, ***",
"entpCodes": "***, ***",
"invoCode": "***",
"invono": "***, ***",
"prodNames": "***, ***",
"prodentpNames": "***, ***",
"pubonlnProdIDs": "***, ***",
"hisDrugIds": "***, ***"
}
```

**出参:**

```json
{
"returnCode": 0,
"returnMsg": "***",
"totalPageCount": "***",
"totalRecordCount": "***",
"currentPageNumber": "***",
"resultList": [
{
"ordNo": 0,
"ordDetlCode": "***",
"hisOrdNo": "***",
"hisUniquekey": "***",
"ordDetlStas": "***",
"sbmtTime": "***",
"stroomID": "***",
"stroomName": "***",
"medinsCode": "***",
"medinsName": "***",
"shpNo": "***",
"shpDetlNo": "***",
"hisDrugId": "***",
"pubonlnProdID": "***",
"ver": "***",
"drugUnino": "***",
"prodName": "***",
"dosform": "***",
"spec": "***",
"pac": "***",
"pacmatl": "***",
"prodentpName": "***",
"itemname": "***",
"isCapacity": "***",
"pubonlnPric": "***",
"purcPrice": "***",
"hisPurcPrice": "***",
"transformNum": "***",
"purcAmt": "***",
"purcCnt": "***",
"shpCnt": "***",
"shpAmt": "***",
"shppCnt": "***",
"shppTime": "***",
"shpStas": "***",
"invono": "***",
"invoCode": "***",
"bchno": "***",
"expy": "***",
"productionDate": "***",
"entpCode": "***",
"entpName": "***",
"hisEntpCode": "***",
"hisEntpName": "***",
"drugTracCodg": "***",
"drugTracCodgList": [
{
"drugIdentificationCode": "***",
"drugTracCodg": "***",
"tracCodgPacHery": "***"
}
],
"incNum": "***"
}
]
}
```

---

## 第5章 字典枚举说明

### 5.1 返回编号状态枚举

| 序号 | 枚举值 | 枚举名称 | 枚举名称 |
|---|---|---|---|
| 1 | 0 | 成功 |  |
| 2 | 1 | 失败 |  |

---

## 第6章 门诊接口

### 6.1 门诊发药处方接口或视图 (his 提供必须)

#### 6.1.1.1 接口说明

用于获取发药处方明细数据, 用于采集追溯码和上传医保两定。

#### 6.1.1.2 接口地址

接口地址: his 提供

#### 6.1.1.3 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 2 | cfxh | 处方序号 | 字符型 | 50 | N |  |
|  | `└─dept_id` | 科室 Id | 字符型 | 50 | N |  |
| 5 | patient_id | 患者 id | 字符型 | 50 | N | 通过患者 id 获取 数据, 获取可以 发药的数据 |
| 6 | start time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 7 | end time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |

#### 6.1.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| med_list_codg | 医疗目录编码 | 字符型 | 50 | Y |  |
| fixmedins_hilist_id | 定点医药机构目录编号 | 字符型 | 30 | Y |  |
| fixmedins_hilist_name | 定点医药机构目录名称 | 字符型 | 200 | Y |  |
| fixmedins_bchno | 定点医药机构批次流水号 | 字符型 | 30 | Y | 当前视图数据唯一值(不可以重复), 建议取处方唯一值和处方明细唯一值组合 |
| prsc_dr_certno | 开方医师证件号码 | 字符型 | 50 |  |  |
| prsc_dr_name | 开方医师姓名 | 字符型 | 50 | Y |  |
| phar_certno | 药师证件号码 | 字符型 | 50 |  |  |
| phar_name | 药师姓名 | 字符型 | 50 | Y |  |
| phar_prac_cert_no | 药师执业资格证号 | 字符型 | 50 | Y |  |
| mdtrt_sn | 就医流水号 | 字符型 | 30 | Y | 医保结算时为 MDTRT_ID, 自费结算时为医疗机构内就诊流水号 |
| psn_name | 人员姓名 | 字符型 | 50 |  |  |
| manu_lotnum | 生产批号 | 字符型 | 30 | Y |  |
| manu_date | 生产日期 | 日期型 |  | Y | yyyy-MM-dd |
| expy_end | 有效期止 | 日期型 |  | Y | yyyy-MM-dd |
| rx_flag | 处方药标志 | 字符型 | 3 | Y | 0-否; 1-是 |
| trdn_flag | 拆零标志 | 字符型 | 3 | Y | 0-否; 1-是 |
| rxno | 处方号 | 字符型 | 40 |  |  |
| rx_circ_flag | 外购处方标志 | 字符型 | 3 |  |  |
| rtal_docno | 零售单据号 | 字符型 | 40 | Y |  |
| stoout_no | 销售出库单据号 | 字符型 | 40 |  |  |
| bchno | 批次号 | 字符型 | 30 |  |  |
| sel_retn_cnt | 销售/退货数量 | 数值型 | 16,4 | Y |  |
| min_sel_retn_cnt | 最小单位销售数量 | 数值型 | 16,4 |  |  |
| sel_retn_unit | 发药时候的单位 | 字符型 | 6 | Y |  |
| his_dos_unit | His 的剂量单位 | 字符型 | 6 | Y |  |
| his_pac_unit | His 的包装单位 | 字符型 | 6 | Y |  |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | yyyy-MM-dd HH:mm:ss |
| sel_retn_opter_name | 销售/退货经办人姓名 | 字符型 | 50 | Y |  |
| mdtrt_setl_type | 就诊结算类型 | 字符型 | 6 | Y | 1-医保结算 2-自费结算 |
| spec | 规格 | 字符型 | 50 | Y |  |
| prodentp_name | 生产企业名称 | 字符型 | 50 | Y |  |
| cfxh | 处方序号 | 字符型 | 50 | Y |  |
| cfmxxh | 处方明细序号 | 字符型 | 50 | Y |  |
| sjh | 收据号 | 字符型 | 50 | Y | 处方小票扫码值 |
| patient_id | 患者 id | 字符型 | 50 | Y | 处方小票扫码值 (和 sjh 取一个即可) |
| his_con_ratio | 最小单位转换比(制剂单位和包装单位的转换比) | 数值型 |  | 建议 |  |
| send_flag | 发药标志 | 字符型 | 20 | 建议 | 0:待发药, 1:已发药, 7:部分退药, 8:全部退药, 9:作废 |
| send_time | 发药时间 | 日期时间型 |  | 建议 | yyyy-MM-dd HH:mm:ss |
| return_time | 退药时间 | 日期时间型 |  | N | yyyy-MM-dd HH:mm:ss |
| dept_id | 发药药房id | 字符型 | 50 | Y |  |
| dept_name | 发药药房名称 | 字符型 | 50 | Y |  |
| window | 窗口号 | 字符型 | 50 | N |  |

### 6.2 门诊退药接口或视图 (his 提供必须)

#### 6.2.1.1 接口说明

用于获取发药处方明细数据, 用于采集追溯码和上传医保两定。

#### 6.1.1.2 接口地址

接口地址: his 提供

#### 6.2.1.3 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必 填 | 说明 |
|---|---|---|---|---|---|
| sjh | 收据号 | 字符型 | 50 | N | 可以通过收据号来获取到当前退 药的信息, 和后续退药单上面的 条码保持一致 |
| patient_id | 患者 id | 字符型 | 50 | Y | 处方小票扫码值 (和 sjh 取一个 即可) |
| start_time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| end_time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |

#### 6.2.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| med_list_codg | 医疗目录编码 | 字符型 | 50 | Y |  |
| fixmedins_hilist_id | 定点医药机构目录编号 | 字符型 | 30 | Y |  |
| fixmedins_hilist_name | 定点医药机构目录名称 | 字符型 | 200 | Y |  |
| fixmedins_bchno | 定点医药机构批次流水号 | 字符型 | 30 | Y | 当前视图数据唯一值（不可以重复），建议取处方唯一值和处方明细唯一值组合 |
| prsc_dr_certno | 开方医师证件号码 | 字符型 | 50 |  |  |
| prsc_dr_name | 开方医师姓名 | 字符型 | 50 | Y |  |
| phar_certno | 药师证件号码 | 字符型 | 50 |  |  |
| phar_name | 药师姓名 | 字符型 | 50 | Y |  |
| phar_prac_cert_no | 药师执业资格证号 | 字符型 | 50 | Y |  |
| mdtrt_sn | 就医流水号 | 字符型 | 30 | Y | 医保结算时为MDTRT_ID，自费结算时为医疗机构内就诊流水号 |
| psn_name | 人员姓名 | 字符型 | 50 |  |  |
| manu_lotnum | 生产批号 | 字符型 | 30 | Y |  |
| manu_date | 生产日期 | 日期型 |  | Y | yyyy-MM-dd |
| expy_end | 有效期止 | 日期型 |  |  | yyyy-MM-dd |
| rx_flag | 处方药标志 | 字符型 | 3 | Y | 0-否；1-是 |
| trdn_flag | 拆零标志 | 字符型 | 3 | Y | 0-否；1-是 |
| rxno | 处方号 | 字符型 | 40 |  |  |
| rx_circ_flag | 外购处方标志 | 字符型 | 3 |  |  |
| rtal_docno | 零售单据号 | 字符型 | 40 | Y |  |
| stoout_no | 销售出库单据号 | 字符型 | 40 |  |  |
| bchno | 批次号 | 字符型 | 30 |  |  |
| sel_retn_cnt | 销售/退货数量 | 数值型 | 16,4 | Y |  |
| min_sel_retn_cnt | 最小单位销售数量 | 数值型 | 16,4 |  |  |
| sel_retn_unit | 发药时候的单位 | 字符型 | 6 | Y |  |
| his_dos_unit | His的剂量单位 | 字符型 | 6 | Y |  |
| his_pac_unit | His的包装单位 | 字符型 | 6 | Y |  |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | yyyy-MM-dd HH:mm:ss |
| sel_retn_opter_name | 销售/退货经办人姓名 | 字符型 | 50 | Y |  |
| mdtrt_setl_type | 就诊结算类型 | 字符型 | 6 | Y | 1-医保结算2-自费结算 |
| spec | 规格 | 字符型 | 50 | Y |  |
| prodentp_name | 生产企业名称 | 字符型 | 50 | Y |  |
| cfxh | 处方序号 | 字符型 | 50 | Y |  |
| cfmxxh | 处方明细序号 | 字符型 | 50 | Y |  |
| sjh | 收据号 | 字符型 | 50 | Y | 处方小票扫码值 |
| patient_id | 患者id | 字符型 | 50 | Y | 处方小票扫码值（和sjh取一个即可） |
| his_con_ratio | 最小单位转换比(制剂单位和包装单位的转换比) | 数值型 |  | Y |  |

---

### 6.3 门诊退药患者接口或视图 (his 提供)

#### 6.3.1.1 接口说明

如果没有退药单, 或者退药单上面不方便加条码的话, 我们作为第三方系统, 如何获取到退药的处方 数据, 所以就会有一个选择当天退药患者的一个功能, 通过手动选择的方式, 来获取到退药处方信息。

#### 6.3.1.2 接口地址

接口地址: his 提供

#### 6.3.1.3 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必 填 | 说明 |
|---|---|---|---|---|---|
| start_time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| end_time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |

#### 6.3.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| patient_id | 患者 id | 字符型 | 50 | Y |  |
| patient_name | 患者名称 | 字符型 | 50 | Y |  |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | yyyy-MM-dd HH:mm:ss |

---

## 第7章 住院追溯码采集

### 7.1 领药单明细视图

#### 7.1.1.1 接口说明

获取到当次病区领药的信息

#### 7.1.1.2 接口地址

接口地址: his 提供

#### 7.1.1.3 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必 填 | 说明 |
|---|---|---|---|---|---|
| dept_id | 发药药房 id | 字符型 | 50 | N | 可以根据筛选出当前药房的全部处方记 录 |
| start time | 开始时间 | 日期时间 型 |  | N | yyyy-MM-dd HH:mm:ss (与 end_time 配合使用, 查询某个时间范围内的发药 记录。如果提供了 record_id, 则忽略 |
| end time | 结束时间 | 日期时间 型 |  | N | yyyy-MM-dd HH:mm:ss (与 start_time 配合使用。如果提供了 record_id, 则 忽略时间范围) |
| pat_ward_i d | 病区 id | 字符型 | 50 | N | 配合筛选 |
| record id | 发药记录 id | 字符型 |  | N | 实际上就是发药单号, 退药的时候, 就 是退药单号, 根据这个发药记录 id 查 询出对应的所有明细数据。如果提供了 此参数, 将优先使用此参数进行查询, 忽略 start_time 和 end_time。注 意:fyyf 或 dept_id 通常需要与 record_id 一起提供以缩小范围 |
| fg_dps | 发药单标记 | 字符型 | 4 | Y | 0:发药, 1:退药 |

#### 7.1.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 药品集合 | Object |  | data 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| record_id | 发药记录id | 字符型 | 50 | Y | 发药记录唯一标识 (原汇总单ID)，必填,实际上就是发药单号，如果是退药的时候，就是退药单号 |
| record_detail_id | 发药明细id | 字符型 | 50 | Y |  |
| ori_detail_id | 原发药明细id | 字符型 | 50 |  | 用于退药单，记录原发药单明细 |
| na_fee | 费用名称 | 字符型 | 50 |  |  |
| sd_classify | 医嘱类别 | 字符型 | 4 |  | 1临时 2长期 |
| fg_dps | 发药单标记 | 字符型 | 4 | Y | 0:发药, 1: 退药 |
| send_flag | 发药标志 | 字符型 | 20 | 建议 | 0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废 |
| send_time | 发药时间 | 日期时间型 |  | 建议 | yyyy-MM-dd HH:mm:ss |
| rtal_docno | 零售单据号 | 字符型 | 40 | Y | 零售单据号 (通常是 record_id 或相关单据号)，必填 |
| stoout_no | 销售出库单据号 | 字符型 | 40 | N | 销售出库单据号 (通常是 record_id 或相关单据号) |
| pat_ward_id | 病区id | 字符型 | 50 | Y | 病区唯一标识 (原汇总单所属病区) |
| pat_ward_name | 病区名称 | 字符型 | 50 | Y | 病区名称 (原汇总单所属病区名称) |
| dept_id | 发药科室id | 字符型 | 50 |  | 发药科室唯一标识 (与 fyyf 意义相同)， |
| phar_certno | 药师证件号码 | 字符型 | 50 | N | 发药药师证件号码 |
| phar_name | 药师姓名 | 字符型 | 50 | Y | 发药药师姓名，必填 |
| phar_prac_cert_no | 药师执业资格证号 | 字符型 | 50 | Y | 发药药师执业资格证号，必填 |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | 销售/退货时间 (发药时间)，格式：yyyy-MM-dd HH:mm:ss，必填 |
| **药品信息** |  |  |  |  | **以下字段描述了该细单对应的药品：** |
| his_drug_code | HIS系统中的药品唯一编码 | 字符型 | 50 | Y | HIS系统中的药品唯一编码，必填 |
| med_list_codg | 医疗目录编码 | 字符型 | 50 | N | 医疗目录编码，如国家医保编码（XC10AAA067A001020105847） |
| spec | 规格 | 字符型 | 50 | Y | 药品规格，必填 |
| prodentp_name | 生产企业名称 | 字符型 | 50 | Y | 生产企业名称，必填 |
| fixmedins_hilist_id | 定点医药机构目录编号 | 字符型 | 30 | Y | 取HIS药品的唯一值编码（如药品内码），必填 |
| fixmedins_hilist_name | 定点医药机构目录名称 | 字符型 | 200 | Y | 取HIS药品的名称，必填 |
| manu_lotnum | 生产批号 | 字符型 | 30 | Y | 生产批号，必填 |
| manu_date | 生产日期 | 日期型 |  | Y | 格式：yyyy-MM-dd，必填 |
| expy_end | 有效期止 | 日期型 |  | N | 格式：yyyy-MM-dd |
| bchno | 批次号 | 字符型 | 30 | N | 批次号 (库房批次号或其他内部批次号) |
| rx_flag | 处方药标志 | 字符型 | 3 | Y | 0-否；1-是，必填 |
| trdn_flag | 拆零标志 | 字符型 | 3 | Y | 0-否；1-是，必填 |
| his_dos_unit | his的剂量单位 | 字符型 | 50 | N | 规格如果是20粒/盒，那么这个就是粒 (建议必填) |
| his_pac_unit | his的包装单位 | 字符型 | 50 | N | 规格如果是20粒/盒，那么这个就是盒 (建议必填) |
| his_con_ratio | 转换比 | 字符型 | 50 | N | 制剂单位和包装单位的转换比，规格如果是20粒/盒，那么这个就是20 (建议必填) |
| **患者及本次发放明细信息** |  |  |  |  | **以下字段描述了本次具体的患者和发放数量信息** |
| fixmedins_bchno | 定点医药机构批次流水号 | 字符型 | 50 | Y | 当前视图数据唯一值（不可以重复），用于唯一标识本次细单记录。 建议取cfxh +cfmxxh 药品编码等组合，以确保在同一批次、同一患者、同一医嘱下，同一种药品的不同发放记录也能唯一区分 (如果存在多次发药给同一患者)。必填。 |
| pat_in_hos_id | 住院id | 字符型 | 50 | Y | 患者住院唯一标识，必填 |
| mdtrt_sn | 就医流水号 | 字符型 | 50 | Y | 医保结算时为MDTRT_ID，自费结算时为医疗机构内就诊流水号，必填 |
| psn_name | 人员姓名 | 字符型 | 50 | N | 人员姓名 |
| bed_no | 床位号 | 字符型 | 50 | N | 床位号 |
| mdtrt_setl_type | 就诊结算类型 | 字符型 | 6 | Y | 1-医保结算，2-自费结算，必填 |
| order_id | 医嘱id | 字符型 | 50 | Y | 医嘱唯一标识，必填 |
| prsc_dr_certno | 开方医师证件号码 | 字符型 | 50 | N | 开方医师证件号码 |
| prsc_dr_name | 开方医师姓名 | 字符型 | 50 | Y | 开方医师姓名，必填 |
| sel_retn_cnt | 销售/退货数量 | 数值型 | 16 | Y | 本次细单对应的数量。 取 his 领药单中该患者该药品的实际发放数量。数值型，必填（比如说给这个患者发了 2 盒，那么就是 2） |
| sel_retn_unit | 销售/退货单位 | 字符型 | 50 | N | 本次细单对应的数量单位。 取 his 领药单中该患者该药品的实际发放单位（比如说 2 盒，单位是盒；5 支，单位是支；30 粒，单位是粒）。建议必填。 |
| cfxh | 医院医嘱主表唯一值 | 字符型 |  | N | （辅助字段，方便 his 关联，如果 his 视图能直接提供可包含） |
| cfmxxh | 医院医嘱明细表唯一值 | 字符型 |  | N | （辅助字段，方便 his 关联，如果 his 视图能直接提供可包含） |

---

### 7.2 住院药房领药记录列表接口或视图 (his 提供) 没法提供发药打印凭条的话

#### 7.2.1.1 接口说明

此接口用于分页获取指定药房在某个时间范围内的发药/退药记录列表。它作为查询明细数 据的前置步骤, 返回的是不包含具体药品明细的概要信息。客户端应先调用此接口获取到 `record_id`(发药记录 ID), 再使用此 ID 调用 7.1 领药单明细接口 来获取完整的药品明细。

#### 7.2.1.2 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|
| dept_id | 发药药房 id | 字符型 | Y |  | 查询指定药房的发药/ 退药记录。 dept_id |
| start_time | 开始时间 | 日期时间 型 | Y | 格式: yyyy-MM-dd HH:mm:ss。查询范围的 start_time 开始时间。 |
| end_time | 结束时间 | 日期时间 型 | Y | 格式: yyyy-MM-dd HH:mm:ss。查询范围的 end_time 结束时间。 |
| pat_ward_id | 病区 id | 字符型 | N | 根据病区 ID 进行筛 选。 pat_ward_id |
| fg_dps | 发药单标记 | 字符型 | Y | 0:发药, 1:退药。 fg_dps |

#### 7.2.1.3 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | dataList 数据 | 数组 |  |  |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| record_id | 发药记录id | 字符型 | Y |  | 发药/退药的唯一记录ID, 用于传递给明细接口。 |
| pat_ward_id | 病区id | 字符型 | Y |  | 该领药单所属的病区ID。 |
| pat_ward_name | 病区名称 | 字符型 | Y |  | 该领药单所属的病区名称。 |
| fg_dps | 发药单标记 | 字符型 | Y |  | 0:发药, 1:退药。 |
| send_flag | 发药标志 | 字符型 | Y |  | 0:待发药, 1:已发药, 7:部分退药, 8:全部退药, 9:作废。 |
| record_time | 记录时间 | 日期时间型 | Y |  | 执行发药或退药操作的时间。格式:yyyy-MM-dd HH:mm:ss |
| phar_name | 药师姓名 | 字符型 | Y |  | 执行发药或退药操作的药师姓名。 |
| summary | 记录摘要 | 字符型 | N |  | 可选。用于前端显示的简要信息, 如"涉及5个患者, 共12种药品"。 |

---

### 7.3 出院带药接口或视图 (his 提供)

#### 7.3.1.1 接口说明

用于获取发药处方明细数据, 用于采集追溯码和上传医保两定。

#### 7.3.1.2 接口地址

接口地址: his 提供

#### 7.3.1.3 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 5 | patient_id | 患者 id | 字符型 | 50 | N | 通过患者 id 获取 数据, 获取可以 发药的数据 |
| 6 | start_time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 7 | end_time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 8 | cydybz | 出院带药标志 | 字符型 | 20 | N | 0正常医嘱, 1出 院带药, 不传代表 所有 |

#### 7.3.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| med_list_codg | 医疗目录编码 | 字符型 | 50 | Y |  |
| fixmedins_hilist_id | 定点医药机构目录编号 | 字符型 | 30 | Y |  |
| fixmedins_hilist_name | 定点医药机构目录名称 | 字符型 | 200 | Y |  |
| fixmedins_bchno | 定点医药机构批次流水号 | 字符型 | 30 | Y | 当前视图数据唯一值（不可以重复），建议取处方唯一值和处方明细唯一值组合 |
| prsc_dr_certno | 开方医师证件号码 | 字符型 | 50 |  |  |
| prsc_dr_name | 开方医师姓名 | 字符型 | 50 | Y |  |
| phar_certno | 药师证件号码 | 字符型 | 50 |  |  |
| phar_name | 药师姓名 | 字符型 | 50 | Y |  |
| phar_prac_cert_no | 药师执业资格证号 | 字符型 | 50 | Y |  |
| mdtrt_sn | 就医流水号 | 字符型 | 30 | Y | 医保结算时为MDTRT_ID，自费结算时为医疗机构内就诊流水号 |
| psn_name | 人员姓名 | 字符型 | 50 |  |  |
| manu_lotnum | 生产批号 | 字符型 | 30 | Y |  |
| manu_date | 生产日期 | 日期型 |  | Y | yyyy-MM-dd |
| expy_end | 有效期止 | 日期型 |  |  | yyyy-MM-dd |
| rx_flag | 处方药标志 | 字符型 | 3 | Y | 0-否；1-是 |
| dept_id | 发药科室id | 字符型 | 50 | Y | 发药科室唯一标识 |
| trdn_flag | 拆零标志 | 字符型 | 3 | Y | 0-否；1-是 |
| rxno | 处方号 | 字符型 | 40 |  |  |
| rx_circ_flag | 外购处方标志 | 字符型 | 3 |  |  |
| rtal_docno | 零售单据号 | 字符型 | 40 | Y |  |
| stoout_no | 销售出库单据号 | 字符型 | 40 |  |  |
| bchno | 批次号 | 字符型 | 30 |  |  |
| sel_retn_cnt | 销售/退货数量 | 数值型 | 16,4 | Y |  |
| min_sel_retn_cnt | 最小单位销售数量 | 数值型 | 16,4 | Y |  |
| sel_retn_unit | 发药时候的单位 | 字符型 | 6 | Y |  |
| his_dos_unit | His的剂量单位 | 字符型 | 6 | Y |  |
| his_pac_unit | His的包装单位 | 字符型 | 6 | Y |  |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | yyyy-MM-dd HH:mm:ss |
| sel_retn_opter_name | 销售/退货经办人姓名 | 字符型 | 50 | Y |  |
| mdtrt_setl_type | 就诊结算类型 | 字符型 | 6 | Y | 1-医保结算2-自费结算 |
| spec | 规格 | 字符型 | 50 | Y |  |
| prodentp_name | 生产企业名称 | 字符型 | 50 | Y |  |
| cfxh | 处方序号 | 字符型 | 50 | Y |  |
| cfmxxh | 处方明细序号 | 字符型 | 50 | Y |  |
| sjh | 收据号 | 字符型 | 50 | Y | 处方小票扫码值 |
| patient_id | 患者id | 字符型 | 50 | Y | 处方小票扫码值（和sjh取一个即可） |
| his_con_ratio | 最小单位转换比 | 数值型 |  |  | 建议 |
| cydybz | 出院带药标志 | 字符型 | 20 | Y | 0正常医嘱，1 出院带药 |

---

### 7.4 出院结算采集

#### 7.4.1 出院结算用户列表接口或视图 (his 提供必须)

##### 7.4.1.1 接口说明

用于获取出院结算用户列表, 可以通过该接口的出参住院号。默认获取当天的数据, 可以根据日期范围查询。

##### 7.4.1.2 接口地址

接口地址: his 提供

##### 7.4.1.3 输入

说明: 具体医院实际请求参数根据对接情况调整

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 1 | start_time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 2 | end_time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |

##### 7.4.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 序号 | 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|---|
|  | pat_in_hos_id | 住院 id | 字符型 | 50 | Y | 患者住院唯一标识 |
|  | patient_id | 患者 id | 字符型 | 50 | Y |  |
|  | patient_name | 患者名称 | 字符型 | 50 | Y |  |
|  | sel_time | 结算时间 | 日期型 |  | Y | yyyy-MM-dd HH:mm:ss |

#### 7.4.2 出院结算明细列表接口或者视图 (his 提供必须)

##### 7.4.2.1 接口说明

用于获取出院结算医嘱明细数据, 用于拆零追溯码赋码和上传医保两定。

流程说明:
模式 1、先通过出院结算用户列表接口获取到对应患者的住院号, 然后通过住院号调用该接口获取需要处理追溯码的医嘱明细数据。(可以在住院药房专人操作, 如出现拆零赋码无库存, 可以用 pda 进行扫码)
模式 2、增加住院号条形码, 出院结算的时候进行赋拆零追溯码码。(根据医院情况而定)

##### 7.4.2.2 接口地址

接口地址: his 提供

##### 7.4.2.3 输入

说明: 具体医院实际请求参数根据对接情况调整

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
| 5 | patient_id | 患者 id | 字符型 | 50 | N | 通过患者 id 获取数据, 获取可以发药的数据 |
| 6 | start_time | 开始时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 7 | end_time | 结束时间 | 字符型 | 20 | N | yyyy-MM-dd HH:mm:ss |
| 8 | cydybz | 出院带药标志 | 字符型 | 20 | N | 0 正常医嘱, 1 出院带药, 不传代表所有 |
| 9 | pat_in_hos_id | 住院 id | 字符型 | 50 | Y | 患者住院唯一标识 |

##### 7.4.2.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| id | 费用明细 id | 字符型 | 50 | Y |  |
| med_list_codg | 医疗目录编码 | 字符型 | 50 | Y |  |
| fixmedins_hilist_id | 定点医药机构目录编号 | 字符型 | 30 | Y |  |
| fixmedins_hilist_name | 定点医药机构目录名称 | 字符型 | 200 | Y |  |
| fixmedins_bchno | 定点医药机构批次流水号 | 字符型 | 30 | Y | 当前视图数据唯一值（不可以重复），建议取处方唯一值和处方明细唯一值组合. 和住院领药单里面的 fixmedins_bchno 关联 |
| prsc_dr_certno | 开方医师证件号码 | 字符型 | 50 |  |  |
| prsc_dr_name | 开方医师姓名 | 字符型 | 50 | Y |  |
| phar_certno | 药师证件号码 | 字符型 | 50 |  |  |
| phar_name | 药师姓名 | 字符型 | 50 | Y |  |
| phar_prac_cert_no | 药师执业资格证号 | 字符型 | 50 | Y |  |
| mdtrt_sn | 就医流水号 | 字符型 | 30 | Y | 医保结算时为 MDTRT_ID，自费结算时为医疗机构内就诊流水号 |
| psn_name | 人员姓名 | 字符型 | 50 |  |  |
| manu_lotnum | 生产批号 | 字符型 | 30 | Y |  |
| manu_date | 生产日期 | 日期型 |  | Y | yyyy-MM-dd |
| expy_end | 有效期止 | 日期型 |  |  | yyyy-MM-dd |
| rx_flag | 处方药标志 | 字符型 | 3 | Y | 0-否；1-是 |
| trdn_flag | 拆零标志 | 字符型 | 3 | Y | 0-否；1-是 |
| rxno | 处方号 | 字符型 | 40 |  |  |
| rx_circ_flag | 外购处方标志 | 字符型 | 3 |  |  |
| rtal_docno | 零售单据号 | 字符型 | 40 | Y |  |
| stoout_no | 销售出库单据号 | 字符型 | 40 |  |  |
| bchno | 批次号 | 字符型 | 30 |  |  |
| sel_retn_cnt | 销售/退货数量 | 数值型 | 16,4 | Y |  |
| min_sel_retn_cnt | 最小单位销售数量 | 数值型 | 16,4 | Y |  |
| sel_retn_unit | 发药时候的单位 | 字符型 | 6 | Y |  |
| his_dos_unit | His 的剂量单位 | 字符型 | 6 | Y |  |
| his_pac_unit | His 的包装单位 | 字符型 | 6 | Y |  |
| sel_retn_time | 销售/退货时间 | 日期时间型 |  | Y | yyyy-MM-dd HH:mm:ss |
| dept_id | 发药科室 id | 字符型 | 50 | Y | 发药科室唯一标识 |
| sel_retn_opter_name | 销售/退货经办人姓名 | 字符型 | 50 | Y |  |
| mdtrt_setl_type | 就诊结算类型 | 字符型 | 6 | Y | 1-医保结算 2-自费结算 |
| spec | 规格 | 字符型 | 50 | Y |  |
| prodentp_name | 生产企业名称 | 字符型 | 50 | Y |  |
| cfxh | 处方序号 | 字符型 | 50 | Y |  |
| cfmxxh | 处方明细序号 | 字符型 | 50 | Y |  |
| sjh | 收据号 | 字符型 | 50 | Y | 处方小票扫码值 |
| patient_id | 患者 id | 字符型 | 50 | Y | 处方小票扫码值（和 sjh 取一个即可） |
| his_con_ratio | 最小单位转换比 | 数值型 |  | 建议 |  |
| cydybz | 出院带药标志 | 字符型 | 20 | Y | 0 正常医嘱，1 出院带药 |
| pat_in_hos_id | 住院 id | 字符型 | 50 | Y | 患者住院唯一标识 |

---

## 第8章 调拨库存接口

### 8.1 药品出入库接口或者视图 (his 提供)

#### 8.1.1.1 接口说明

院内所有药品的流转变动、药品从药库调拨到药房, 或者各个药房之间调拨流转。

#### 8.1.1.2 接口地址

接口地址: his 提供, 改成主子表的方式

#### 8.1.1.3 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|
| transfer_no | 调拨单唯一编号 | String |  | Y |  |
| transfer_time | 调拨时间 | 日期时间型 |  |  | yyyy-MM-dd HH:mm:ss |
| sd_flow_sub | 调拨类型 | String |  |  | 1: 采购,2: 申领,3:盘盈,4:其它入库,5.采购退库,6.调拨,7:盘亏,8:其它出库,9:破损 |
| from_dept_id | 当前库房ID | String |  | Y |  |
| to_dept_id | 目标库房ID | String |  |  |  |
| create_by | 操作员名称 | String |  |  |  |
| remarks | 调拨备注 | String |  |  |  |
| drugList | 药品集合 | 数组 |  | Y | 包含多个药品信息的数组 |
| drug_code | 药品主键 | String |  | Y |  |
| bchno | 药品批号 | String |  |  |  |
| expy | 药品效期 | 日期 |  |  | yyyy-MM-dd |
| quantity | 调拨数量 | Integer |  | Y |  |

#### 8.1.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |


### 8.2 药品库存接口或视图 (his 提供)

#### 8.2.1.1 接口说明

查询药品在库房中的数量。

#### 8.2.1.2 接口地址

接口地址: his 提供

#### 8.2.1.3 输入

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|
| drug_code | 药品主键 (唯一编 码) | String | 50 | Y |  |
| dept_id | 库房 ID | String | 50 | Y |  |
| bchno | 药品批号 | String |  |  |  |

#### 8.2.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|
| drug_code | 药品主键（唯一编码） | String |  |  |
| drug_name | 药品名称 | String |  |  |
| dept_id | 库房ID | String |  |  |
| id_sto_inv | 库存ID | String |  |  |
| dept_name | 库房名称 | String |  |  |
| bchno | 批号 | 字符型 |  |  |
| expy | 有效期 | 日期型 |  |  |
| production_date | 生产日期 | 日期型 |  |  |
| quantity | 库存数量 | Integer |  |  |
| unit_flow | 库存单位 | String |  |  |
| update_time | 库存信息最后更新时间 | 时间日期型 |  |  |
| remark | 备注 | String |  |  |

---

## 第9章 字典接口

### 9.1 科室字典接口或视图 (his 提供前期可以提供表格)

#### 9.1.1.1 接口说明

获取各个科室的基本信息, 用于区分门诊住院药房和药库。

#### 9.1.1.2 接口地址

接口地址: his 提供

#### 9.1.1.3 输入

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 是否必填 | 说明 |
|---|---|---|---|---|---|---|
|  | parentId | 上级科室 id | 字符串 | 30 | N |  |
| 1 | dept_id | 科室 ID | 字符串 | 30 | N |  |
| 2 | dept_code | 科室编号 | 字符串 | 30 | N |  |
|  | dept_name | 科室名称 | 字符串 | 30 | N |  |

#### 9.1.1.4 输出

| 序号 | 参数代码 | 参数名称 | 参数类型 | 参数长度 | 说明 |
|---|---|---|---|---|---|
| 1 | code | 返回编号 | 数值型 | 4 | 0 成功, 1 失败 |
| 2 | message | 返回信息 | 字符型 | 100 |  |
| 3 | dataList | 处方明细数组 | 数组 |  | dataList 数据 |

##### dataList 数据

| 参数代码 | 参数名称 | 类型 | 长度 | 必填 | 说明 |
|---|---|---|---|---|---|
| parentId | 上级科室 id | 字符串 | 30 | N |  |
| dept_id | 科室 ID | 字符串 | 30 | N |  |
| dept_code | 科室编号 | 字符串 | 30 | N |  |
| dept_name | 科室名称 | 字符串 | 30 | N |  |
