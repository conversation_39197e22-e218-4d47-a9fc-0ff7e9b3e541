package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhReturnRecordsMz;
import com.zsm.mapper.YsfDmhReturnRecordsMzMapper;
import com.zsm.service.YsfDmhReturnRecordsMzService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhReturnRecordsMzServiceImpl extends ServiceImpl<YsfDmhReturnRecordsMzMapper, YsfDmhReturnRecordsMz> implements YsfDmhReturnRecordsMzService {
}


