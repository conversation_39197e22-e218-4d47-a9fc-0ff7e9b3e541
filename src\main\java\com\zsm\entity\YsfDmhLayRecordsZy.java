package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 住院-领药明细视图 V_YSF_DMH_LAY_RECORDS_ZY
 */
@Data
@TableName("ZHIYDBA.V_YSF_DMH_LAY_RECORDS_ZY")
@Schema(description = "住院-领药明细视图")
public class YsfDmhLayRecordsZy implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("RECORD_ID")
    @Schema(description = "发药记录id（发药/退药单号）")
    private String recordId;

    @TableField("RECORD_DETAIL_ID")
    @Schema(description = "发药明细id（记录唯一明细）")
    private String recordDetailId;

    @TableField("ORI_DETAIL_ID")
    @Schema(description = "原发药明细id（退药关联原发药）")
    private String oriDetailId;

    @TableField("NA_FEE")
    @Schema(description = "费用名称")
    private String naFee;

    @TableField("SD_CLASSIFY")
    @Schema(description = "医嘱类别")
    private String sdClassify;

    @TableField("FG_DPS")
    @Schema(description = "代配标志 0-否 1-是")
    private String fgDps;

    @TableField("SEND_FLAG")
    @Schema(description = "发药标志 0待发药 1已发药 7部分退药 8全部退药 9作废")
    private String sendFlag;

    @TableField("SEND_TIME")
    @Schema(description = "发药时间")
    private String sendTime;

    @TableField("RTAL_DOCNO")
    @Schema(description = "零售单据号")
    private String rtalDocno;

    @TableField("STOOUT_NO")
    @Schema(description = "销售出库单据号")
    private String stooutNo;

    @TableField("PAT_WARD_ID")
    @Schema(description = "病区id")
    private String patWardId;

    @TableField("PAT_WARD_NAME")
    @Schema(description = "病区名称")
    private String patWardName;

    @TableField("DEPT_ID")
    @Schema(description = "发药科室id")
    private String deptId;

    @TableField("PHAR_CERTNO")
    @Schema(description = "药师证件号码")
    private String pharCertno;

    @TableField("PHAR_NAME")
    @Schema(description = "药师姓名")
    private String pharName;

    @TableField("PHAR_PRAC_CERT_NO")
    @Schema(description = "药师执业资格证号")
    private String pharPracCertNo;

    @TableField("SEL_RETN_TIME")
    @Schema(description = "销售/退货时间 yyyy-MM-dd HH:mm:ss")
    private String selRetnTime;

    @TableField("HIS_DRUG_CODE")
    @Schema(description = "HIS 药品编码")
    private String hisDrugCode;

    @TableField("MED_LIST_CODG")
    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @TableField("SPEC")
    @Schema(description = "规格")
    private String spec;

    @TableField("PRODENTP_NAME")
    @Schema(description = "生产企业名称")
    private String prodentpName;

    @TableField("FIXMEDINS_HILIST_ID")
    @Schema(description = "定点医药机构目录编号（HIS药品唯一值）")
    private String fixmedinsHilistId;

    @TableField("FIXMEDINS_HILIST_NAME")
    @Schema(description = "定点医药机构目录名称（HIS药品名称）")
    private String fixmedinsHilistName;

    @TableField("MANU_LOTNUM")
    @Schema(description = "生产批号")
    private String manuLotnum;

    @TableField("MANU_DATE")
    @Schema(description = "生产日期 yyyy-MM-dd")
    private String manuDate;

    @TableField("EXPY_END")
    @Schema(description = "有效期止 yyyy-MM-dd")
    private String expyEnd;

    @TableField("BCHNO")
    @Schema(description = "批次号")
    private String bchno;

    @TableField("RX_FLAG")
    @Schema(description = "处方药标志 0-否 1-是")
    private String rxFlag;

    @TableField("TRDN_FLAG")
    @Schema(description = "拆零标志 0-否 1-是")
    private String trdnFlag;

    @TableField("HIS_DOS_UNIT")
    @Schema(description = "HIS 的剂量单位")
    private String hisDosUnit;

    @TableField("HIS_PAC_UNIT")
    @Schema(description = "HIS 的包装单位")
    private String hisPacUnit;

    @TableField("HIS_CON_RATIO")
    @Schema(description = "最小单位转换比")
    private String hisConRatio;

    @TableField("FIXMEDINS_BCHNO")
    @Schema(description = "定点医药机构批次流水号（记录唯一）")
    private String fixmedinsBchno;

    @TableField("PAT_IN_HOS_ID")
    @Schema(description = "住院id（住院唯一标识）")
    private String patInHosId;

    @TableField("MDTRT_SN")
    @Schema(description = "就医流水号（医保：MDTRT_ID，自费：院内流水号）")
    private String mdtrtSn;

    @TableField("PSN_NAME")
    @Schema(description = "人员姓名")
    private String psnName;

    @TableField("BED_NO")
    @Schema(description = "床号")
    private String bedNo;

    @TableField("MDTRT_SETL_TYPE")
    @Schema(description = "就诊结算类型 1-医保结算 2-自费结算")
    private String mdtrtSetlType;

    @TableField("ORDER_ID")
    @Schema(description = "医嘱明细ID（医嘱号-序号）")
    private String orderId;

    @TableField("PRSC_DR_CERTNO")
    @Schema(description = "开方医师证件号码")
    private String prscDrCertno;

    @TableField("PRSC_DR_NAME")
    @Schema(description = "开方医师姓名")
    private String prscDrName;

    @TableField("SEL_RETN_CNT")
    @Schema(description = "销售/退货数量")
    private Integer selRetnCnt;

    @TableField("SEL_RETN_UNIT")
    @Schema(description = "销售/退货单位")
    private String selRetnUnit;

    @TableField("CFXH")
    @Schema(description = "处方序号")
    private String cfxh;

    @TableField("CFMXXH")
    @Schema(description = "处方明细序号")
    private Integer cfmxxh;

    @TableField("SETL_STATUS")
    @Schema(description = "结算状态")
    private String setlStatus;

    @TableField("SETL_TIME")
    @Schema(description = "结算时间")
    private String setlTime;

    @TableField("SETL_UPLOAD_TIME")
    @Schema(description = "结算上传时间")
    private String setlUploadTime;

    @TableField("PSN_NO")
    @Schema(description = "人员编号（医保人员编号）")
    private String psnNo;
}


