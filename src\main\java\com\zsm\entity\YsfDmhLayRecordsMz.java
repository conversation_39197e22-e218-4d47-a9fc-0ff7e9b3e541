package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 门诊-领药明细视图 V_YSF_DMH_LAY_RECORDS_MZ
 * 对应智业视图的门诊发药处方明细，用于追溯码采集与两定上传
 */
@Data
@TableName("V_YSF_DMH_LAY_RECORDS_MZ")
@Schema(description = "门诊-领药明细视图")
public class YsfDmhLayRecordsMz implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 基本药品字段（参考文档6.1 输出字段）

    /** 医疗目录编码 */
    @TableField("MED_LIST_CODG")
    @Schema(description = "医疗目录编码")
    private String medListCodg;

    /** 定点医药机构目录编号（建议取HIS药品唯一值编码） */
    @TableField("FIXMEDINS_HILIST_ID")
    @Schema(description = "定点医药机构目录编号（HIS药品唯一值）")
    private String fixmedinsHilistId;

    /** 定点医药机构目录名称（建议取HIS药品名称） */
    @TableField("FIXMEDINS_HILIST_NAME")
    @Schema(description = "定点医药机构目录名称（HIS药品名称）")
    private String fixmedinsHilistName;

    /** 定点医药机构批次流水号（本条记录唯一值，建议cfxh+cfmxxh组合） */
    @TableField("FIXMEDINS_BCHNO")
    @Schema(description = "定点医药机构批次流水号（本条记录唯一值）")
    private String fixmedinsBchno;

    /** 开方医师证件号码 */
    @TableField("PRSC_DR_CERTNO")
    @Schema(description = "开方医师证件号码")
    private String prscDrCertno;

    /** 开方医师姓名 */
    @TableField("PRSC_DR_NAME")
    @Schema(description = "开方医师姓名")
    private String prscDrName;

    /** 药师证件号码 */
    @TableField("PHAR_CERTNO")
    @Schema(description = "药师证件号码")
    private String pharCertno;

    /** 药师姓名 */
    @TableField("PHAR_NAME")
    @Schema(description = "药师姓名")
    private String pharName;

    /** 药师执业资格证号 */
    @TableField("PHAR_PRAC_CERT_NO")
    @Schema(description = "药师执业资格证号")
    private String pharPracCertNo;

    /** 就医流水号（医保结算时为MDTRT_ID，自费为院内就诊流水号） */
    @TableField("MDTRT_SN")
    @Schema(description = "就医流水号（医保：MDTRT_ID，自费：院内流水号）")
    private String mdtrtSn;

    /** 人员姓名 */
    @TableField("PSN_NAME")
    @Schema(description = "人员姓名")
    private String psnName;

    /** 生产批号 */
    @TableField("MANU_LOTNUM")
    @Schema(description = "生产批号")
    private String manuLotnum;

    /** 生产日期 yyyy-MM-dd */
    @TableField("MANU_DATE")
    @Schema(description = "生产日期 yyyy-MM-dd")
    private String manuDate;

    /** 有效期止 yyyy-MM-dd */
    @TableField("EXPY_END")
    @Schema(description = "有效期止 yyyy-MM-dd")
    private String expyEnd;

    /** 处方药标志 0-否 1-是 */
    @TableField("RX_FLAG")
    @Schema(description = "处方药标志 0-否 1-是")
    private String rxFlag;

    /** 拆零标志 0-否 1-是 */
    @TableField("TRDN_FLAG")
    @Schema(description = "拆零标志 0-否 1-是")
    private String trdnFlag;

    /** 处方号 */
    @TableField("RXNO")
    @Schema(description = "处方号")
    private String rxno;

    /** 外购处方标志 */
    @TableField("RX_CIRC_FLAG")
    @Schema(description = "外购处方标志")
    private String rxCircFlag;

    /** 零售单据号 */
    @TableField("RTAL_DOCNO")
    @Schema(description = "零售单据号")
    private String rtalDocno;

    /** 销售出库单据号 */
    @TableField("STOOUT_NO")
    @Schema(description = "销售出库单据号")
    private String stooutNo;

    /** 批次号 */
    @TableField("BCHNO")
    @Schema(description = "批次号")
    private String bchno;

    /** 销售/退货数量 */
    @TableField("SEL_RETN_CNT")
    @Schema(description = "销售/退货数量")
    private String selRetnCnt;

    /** 最小单位销售数量 */
    @TableField("MIN_SEL_RETN_CNT")
    @Schema(description = "最小单位销售数量")
    private String minSelRetnCnt;

    /** 发药时候的单位 */
    @TableField("SEL_RETN_UNIT")
    @Schema(description = "发药时候的单位")
    private String selRetnUnit;

    /** His 的剂量单位 */
    @TableField("HIS_DOS_UNIT")
    @Schema(description = "HIS 的剂量单位")
    private String hisDosUnit;

    /** His 的包装单位 */
    @TableField("HIS_PAC_UNIT")
    @Schema(description = "HIS 的包装单位")
    private String hisPacUnit;

    /** 销售/退货时间 yyyy-MM-dd HH:mm:ss */
    @TableField("SEL_RETN_TIME")
    @Schema(description = "销售/退货时间 yyyy-MM-dd HH:mm:ss")
    private String selRetnTime;

    /** 销售/退货经办人姓名 */
    @TableField("SEL_RETN_OPTER_NAME")
    @Schema(description = "销售/退货经办人姓名")
    private String selRetnOpterName;

    /** 就诊结算类型 1-医保结算 2-自费结算 */
    @TableField("MDTRT_SETL_TYPE")
    @Schema(description = "就诊结算类型 1-医保结算 2-自费结算")
    private String mdtrtSetlType;

    /** 规格 */
    @TableField("SPEC")
    @Schema(description = "规格")
    private String spec;

    /** 生产企业名称 */
    @TableField("PRODENTP_NAME")
    @Schema(description = "生产企业名称")
    private String prodentpName;

    /** 处方序号 */
    @TableField("CFXH")
    @Schema(description = "处方序号")
    private String cfxh;

    /** 处方明细序号 */
    @TableField("CFMXXH")
    @Schema(description = "处方明细序号")
    private String cfmxxh;

    /** 收据号（处方小票扫码值） */
    @TableField("SJH")
    @Schema(description = "收据号（处方小票扫码值）")
    private String sjh;

    /** 患者ID */
    @TableField("PATIENT_ID")
    @Schema(description = "患者ID")
    private String patientId;

    /** 最小单位转换比（制剂单位与包装单位的转换比） */
    @TableField("HIS_CON_RATIO")
    @Schema(description = "最小单位转换比（制剂单位与包装单位的转换比）")
    private String hisConRatio;

    /** 发药标志 0:待发药 1:已发药 7:部分退药 8:全部退药 9:作废 */
    @TableField("SEND_FLAG")
    @Schema(description = "发药标志 0待发药 1已发药 7部分退药 8全部退药 9作废")
    private String sendFlag;

    /** 发药时间 */
    @TableField("SEND_TIME")
    @Schema(description = "发药时间")
    private String sendTime;

    /** 退药时间 */
    @TableField("RETURN_TIME")
    @Schema(description = "退药时间")
    private String returnTime;

    /** 发药药房id */
    @TableField("FYYF")
    @Schema(description = "发药药房id")
    private String fyyf;

    /** 发药药房名称 */
    @TableField("FYYF_NAME")
    @Schema(description = "发药药房名称")
    private String fyyfName;

    /** 发药科室id */
    @TableField("DEPT_ID")
    @Schema(description = "发药科室id")
    private String deptId;

    /** 发药科室名称 */
    @TableField("DEPT_NAME")
    @Schema(description = "发药科室名称")
    private String deptName;

    /** 窗口号 */
    @TableField("WINDOW_NO")
    @Schema(description = "窗口号")
    private String windowNo;

    /** 结算状态 1已结算等 */
    @TableField("SETL_STATUS")
    @Schema(description = "结算状态")
    private String setlStatus;

    /** 结算时间 */
    @TableField("SETL_TIME")
    @Schema(description = "结算时间")
    private String setlTime;

    /** 结算上传时间 */
    @TableField("SETL_UPLOAD_TIME")
    @Schema(description = "结算上传时间")
    private String setlUploadTime;

    /** 住院id（住院唯一标识） */
    @TableField("PAT_IN_HOS_ID")
    @Schema(description = "住院id（住院唯一标识）")
    private String patInHosId;

    /** 人员编号（医保人员编号） */
    @TableField("PSN_NO")
    @Schema(description = "人员编号（医保人员编号）")
    private String psnNo;
}


