package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhReturnRecordsZy;
import com.zsm.mapper.YsfDmhReturnRecordsZyMapper;
import com.zsm.service.YsfDmhReturnRecordsZyService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhReturnRecordsZyServiceImpl extends ServiceImpl<YsfDmhReturnRecordsZyMapper, YsfDmhReturnRecordsZy> implements YsfDmhReturnRecordsZyService {
}


