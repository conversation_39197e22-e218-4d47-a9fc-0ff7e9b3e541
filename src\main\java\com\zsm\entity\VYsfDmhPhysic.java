package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 药品字典视图 V_YSF_DMH_PHYSIC
 */
@Data
@TableName("ZHIYDBA.V_YSF_DMH_PHYSIC")
@Schema(description = "药品字典视图")
public class VYsfDmhPhysic implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("hisDrugId")
    @Schema(description = "HIS药品ID")
    private String hisDrugId;

    @TableField("hisEnterpriseCode")
    @Schema(description = "HIS企业编码")
    private String hisEnterpriseCode;

    @TableField("hisEnterpriseName")
    @Schema(description = "HIS企业名称")
    private String hisEnterpriseName;

    @TableField("hisDrugName")
    @Schema(description = "HIS药品名称")
    private String hisDrugName;

    @TableField("hisDrugCountryCode")
    @Schema(description = "HIS药品国家编码")
    private String hisDrugCountryCode;

    @TableField("hisDrugCountryName")
    @Schema(description = "HIS药品国家名称")
    private String hisDrugCountryName;

    @TableField("hisDrugSpec")
    @Schema(description = "HIS药品规格")
    private String hisDrugSpec;

    @TableField("hisPac")
    @Schema(description = "HIS包装")
    private String hisPac;

    @TableField("hisDrugManufacturerCode")
    @Schema(description = "HIS药品生产厂家编码")
    private String hisDrugManufacturerCode;

    @TableField("hisDrugManufacturerName")
    @Schema(description = "HIS药品生产厂家名称")
    private String hisDrugManufacturerName;

    @TableField("hisPurchaseUnit")
    @Schema(description = "HIS采购单位")
    private String hisPurchaseUnit;

    @TableField("hisPurchasePrice")
    @Schema(description = "HIS采购价格")
    private BigDecimal hisPurchasePrice;

    @TableField("hisDoseForm")
    @Schema(description = "HIS剂型")
    private String hisDoseForm;

    @TableField("hisApprovalNum")
    @Schema(description = "HIS批准文号")
    private String hisApprovalNum;

    @TableField("hisDosUnit")
    @Schema(description = "HIS剂量单位")
    private String hisDosUnit;

    @TableField("hisPacUnit")
    @Schema(description = "HIS包装单位")
    private String hisPacUnit;

    @TableField("hisConRatio")
    @Schema(description = "HIS转换比例")
    private Integer hisConRatio;

    @TableField("wholeQuantity")
    @Schema(description = "整体数量")
    private Integer wholeQuantity;

    @TableField("memo")
    @Schema(description = "备注")
    private String memo;

    @TableField("delflag")
    @Schema(description = "删除标志 0-未删除 1-已删除")
    private Integer delflag;

    @TableField("PUBLIC_MSG")
    @Schema(description = "公共消息")
    private String publicMsg;

    @TableField("PHYSIC_CODE")
    @Schema(description = "药品编码")
    private String physicCode;
}
