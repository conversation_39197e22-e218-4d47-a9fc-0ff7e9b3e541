package com.zsm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsm.common.exception.BusinessException;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.*;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.enums.DispenseOrderStatusEnum;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.constant.SyncAccountEnum;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.service.*;
import com.zsm.utils.DateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据持久化服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class DataPersistenceServiceImpl implements DataPersistenceService {

    @Autowired
    private YsfStoDpsService ysfStoDpsService;
    @Autowired
    private YsfStoDpsSubHosService ysfStoDpsSubHosService;
    @Autowired
    private YsfStoTcService ysfStoTcService;
    @Autowired
    private YsfStoTcStatusService ysfStoTcStatusService;
    @Autowired
    private Nhsa3505Service nhsa3505Service;

    /**
     * 批处理大小
     */
    private static final int BATCH_SIZE = 1000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInpatientDispenseData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId, boolean includeNhsa3505) {
        if (CollectionUtils.isEmpty(batch)) {
            log.info("批次数据为空，跳过保存操作");
            return;
        }

        log.info("开始保存住院发药数据到本地数据库，数量: {}, 包含3505表: {}", batch.size(), includeNhsa3505);

        try {
            // 1. 过滤重复数据并进行校验
            List<InPatientDispenseDetailBindScatteredVo> validatedBatch = filterAndValidateBatchData(batch);
            if (CollectionUtils.isEmpty(validatedBatch)) {
                log.info("批次数据全部已处理过或校验失败，跳过后续操作");
                return;
            }

            // 2. 保存发药单主表（ysf_sto_dps）
            List<YsfStoDps> dpsList = new ArrayList<>();
            Map<String, YsfStoDps> dpsMap = new HashMap<>();

            for (InPatientDispenseDetailBindScatteredVo record : validatedBatch) {
                if (!dpsMap.containsKey(record.getCfxh())) {
                    YsfStoDps dps = buildDps(record, account);
                    dpsList.add(dps);
                    dpsMap.put(record.getCfxh(), dps);
                }
            }

            if (!dpsList.isEmpty()) {
                ysfStoDpsService.saveBatch(dpsList);
                log.info("保存发药单主表成功，数量: {}", dpsList.size());
            }

            // 3. 保存发药单明细表（ysf_sto_dps_sub_hos）
            List<YsfStoDpsSubHos> dpsSubHosList = new ArrayList<>();
            Map<String, YsfStoDpsSubHos> dpsSubHosMap = new HashMap<>();

            for (InPatientDispenseDetailBindScatteredVo record : validatedBatch) {
                YsfStoDps dps = dpsMap.get(record.getCfxh());
                YsfStoDpsSubHos dpsSubHos = buildDpsSubHos(record, dps, account);
                if (dps.getIdDps() != null && dpsSubHos.getIdDps() == null) {
                    dpsSubHos.setIdDps(dps.getIdDps().toString());
                }
                dpsSubHosList.add(dpsSubHos);
                dpsSubHosMap.put(record.getCfmxxh(), dpsSubHos);
            }

            if (!dpsSubHosList.isEmpty()) {
                ysfStoDpsSubHosService.saveBatch(dpsSubHosList);
                log.info("保存住院发药单明细表成功，数量: {}", dpsSubHosList.size());
            }

            // 4. 处理追溯码数据
            processTraceCodeData(validatedBatch, account);

            // 5. 根据参数决定是否更新3505表中的批号、有效期、批次号信息
            if (includeNhsa3505) {
                updateNhsa3505BatchInfo(validatedBatch, account, ymfUserId);
                log.info("已保存3505表数据");
            } else {
                log.info("跳过3505表数据保存（已在其他地方处理）");
            }

            log.info("成功保存住院发药数据到本地数据库，数量: {}", validatedBatch.size());

        } catch (Exception e) {
            log.error("保存住院发药数据失败", e);
            throw new BusinessException("保存住院发药数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 过滤重复数据并进行校验
     */
    private List<InPatientDispenseDetailBindScatteredVo> filterAndValidateBatchData(List<InPatientDispenseDetailBindScatteredVo> batch) {
        // 1. 重复处理检查 - 根据cfmxxh查询已处理的数据
        List<String> cfmxxhList = batch.stream()
                .map(InPatientDispenseDetailBindScatteredVo::getCfmxxh)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        final Set<String> processedCfmxxhs;
        if (!cfmxxhList.isEmpty()) {
            LambdaQueryWrapper<YsfStoDpsSubHos> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(YsfStoDpsSubHos::getCfmxxh, cfmxxhList);
            List<YsfStoDpsSubHos> processedRecords = ysfStoDpsSubHosService.list(queryWrapper);

            processedCfmxxhs = processedRecords.stream()
                    .map(YsfStoDpsSubHos::getCfmxxh)
                    .collect(Collectors.toSet());
        } else {
            processedCfmxxhs = new HashSet<>();
        }

        // 2. 过滤掉已处理的数据
        List<InPatientDispenseDetailBindScatteredVo> filteredBatch = batch.stream()
                .filter(record -> !processedCfmxxhs.contains(record.getCfmxxh()))
                .collect(Collectors.toList());

        log.info("原批次数量: {}, 已处理数量: {}, 待处理数量: {}",
                batch.size(), processedCfmxxhs.size(), filteredBatch.size());

        // 3. 对过滤后的数据进行基础校验
        List<InPatientDispenseDetailBindScatteredVo> validatedBatch = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : filteredBatch) {
            try {
                validateSingleRecord(record);
                validatedBatch.add(record);
            } catch (BusinessException e) {
                log.warn("记录校验失败，跳过处理: cfmxxh={}, 原因: {}", record.getCfmxxh(), e.getMessage());
            }
        }

        log.info("校验结果 - 原数量: {}, 校验通过: {}, 校验失败: {}",
                filteredBatch.size(), validatedBatch.size(), filteredBatch.size() - validatedBatch.size());

        return validatedBatch;
    }

    /**
     * 单条记录校验
     */
    private void validateSingleRecord(InPatientDispenseDetailBindScatteredVo record) {
        if (ObjectUtils.isEmpty(record.getRecordId())) {
            throw new BusinessException("发药记录ID不能为空");
        }
        if (ObjectUtils.isEmpty(record.getFixmedinsHilistId())) {
            throw new BusinessException("药品编码不能为空");
        }
        if (ObjectUtils.isEmpty(record.getSelRetnCnt()) || record.getSelRetnCnt() <= 0) {
            throw new BusinessException("发药数量必须大于0");
        }
        if (ObjectUtils.isEmpty(record.getCfxh()) || ObjectUtils.isEmpty(record.getCfmxxh())) {
            throw new BusinessException("处方序号和明细序号不能为空");
        }
    }

    /**
     * 构建发药单对象
     */
    private YsfStoDps buildDps(InPatientDispenseDetailBindScatteredVo record, SyncAccountEnum account) {
        YsfStoDps dps = new YsfStoDps();

        // 基本信息
        dps.setCfxh(record.getCfxh());
        dps.setSdDps(SdDpsEnum.INPATIENT); // 1:住院
        dps.setPatientId(record.getPatInHosId());
        dps.setIdPi(record.getPatInHosId());
        dps.setPsnName(record.getPsnName());
        dps.setPatInHosId(record.getPatInHosId());
        dps.setPatWardId(record.getPatWardId());
        dps.setPatWardName(record.getPatWardName());

        // 发药信息
        if (StringUtils.isNotBlank(record.getSendTime())) {
            dps.setSendTime(DateUtils.parseDateTime(record.getSendTime()));
        }
        dps.setIdDept(record.getDeptId());
        dps.setSelRetnOpterName(record.getPharName());
        dps.setSelRetnOpterId(record.getPharPracCertNo());

        // 状态信息
        dps.setFgStatus(DispenseOrderStatusEnum.DISPENSED.getCode()); // 已发药
        dps.setFgDps("0"); // 0:发药
        dps.setFgPrint("0"); // 未打印

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dps.setIdOrg(nhsaAccount.getMedicalCode());
        dps.setOrgId(nhsaAccount.getMedicalCode());
        dps.setOrgName(nhsaAccount.getMedicalName());
        dps.setCreateTime(LocalDateTime.now());
        dps.setUpdateTime(LocalDateTime.now());
        dps.setCreateBy(account.getUsername());
        dps.setUpdateBy(account.getUsername());
        dps.setDelFlag("0");

        return dps;
    }

    /**
     * 构建发药单明细对象
     */
    private YsfStoDpsSubHos buildDpsSubHos(InPatientDispenseDetailBindScatteredVo record, YsfStoDps dps, SyncAccountEnum account) {
        YsfStoDpsSubHos dpsSubHos = new YsfStoDpsSubHos();

        // 关联信息
        dpsSubHos.setCfmxxh(record.getCfmxxh());
        dpsSubHos.setCfxh(record.getCfxh());
        if (dps.getIdDps() != null) {
            dpsSubHos.setIdDps(dps.getIdDps().toString());
        }
        dpsSubHos.setIdFee(record.getIdFee());
        dpsSubHos.setNaFee(record.getNaFee());

        // 药品信息
        dpsSubHos.setDrugCode(record.getFixmedinsHilistId());

        // 数量信息
        dpsSubHos.setSelRetnCnt(record.getSelRetnCnt());
        dpsSubHos.setQuantity(record.getSelRetnCnt());
        dpsSubHos.setUnit(record.getSelRetnUnit());
        dpsSubHos.setUnitSale(record.getSelRetnUnit());

        // 追溯码信息
        if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
            dpsSubHos.setDrugtracinfo(String.join(",", record.getDrugTracCodgs()));
            dpsSubHos.setTracCnt(record.getDrugTracCodgs().size());
        } else {
            dpsSubHos.setTracCnt(0);
        }

        // 患者信息
        dpsSubHos.setPatInHosId(record.getPatInHosId());
        dpsSubHos.setPatientId(record.getPatInHosId());
        dpsSubHos.setPsnName(record.getPsnName());

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dpsSubHos.setIdOrg(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgId(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgName(nhsaAccount.getMedicalName());
        dpsSubHos.setCreateTime(LocalDateTime.now());
        dpsSubHos.setUpdateTime(LocalDateTime.now());
        dpsSubHos.setCreateBy(account.getUsername());
        dpsSubHos.setUpdateBy(account.getUsername());
        dpsSubHos.setDelFlag("0");

        return dpsSubHos;
    }

    /**
     * 处理追溯码数据（批量优化版本）
     */
    private void processTraceCodeData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account) {
        log.info("开始批量处理追溯码数据，记录数: {}", batch.size());

        // 1. 收集所有需要处理的追溯码信息
        List<TraceCodeProcessInfo> traceCodeInfos = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
                for (String traceCode : record.getDrugTracCodgs()) {
                    TraceCodeProcessInfo info = new TraceCodeProcessInfo();
                    info.setTraceCode(traceCode);
                    info.setRecord(record);
                    traceCodeInfos.add(info);
                }
            }
        }

        if (traceCodeInfos.isEmpty()) {
            log.info("没有需要处理的追溯码数据");
            return;
        }

        log.info("需要处理的追溯码总数: {}", traceCodeInfos.size());

        // 2. 批量查询已存在的追溯码
        List<String> allTraceCodes = traceCodeInfos.stream()
                .map(TraceCodeProcessInfo::getTraceCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, YsfStoTc> existingTcMap = batchQueryExistingTraceCodes(allTraceCodes);

        // 3. 分离新增和更新的追溯码
        List<YsfStoTc> newTcList = new ArrayList<>();
        List<YsfStoTc> updateTcList = new ArrayList<>();
        List<YsfStoTcStatus> newStatusList = new ArrayList<>();
        List<YsfStoTcStatus> existingStatusList = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

        for (TraceCodeProcessInfo info : traceCodeInfos) {
            String traceCode = info.getTraceCode();
            InPatientDispenseDetailBindScatteredVo record = info.getRecord();

            YsfStoTc existingTc = existingTcMap.get(traceCode);

            if (existingTc == null) {
                // 创建新的追溯码记录
                YsfStoTc newTc = buildNewTraceCode(traceCode, record, account, nhsaAccount, now);
                newTcList.add(newTc);

                // 创建对应的状态记录
                YsfStoTcStatus status = buildTraceCodeStatus(traceCode, record, account, null, nhsaAccount, now);
                newStatusList.add(status);
            } else {
                // 更新已存在的追溯码
                existingTc.setUpdateTime(now);
                existingTc.setUpdateBy(account.getUsername());
                updateTcList.add(existingTc);

                // 创建状态记录
                YsfStoTcStatus status = buildTraceCodeStatus(traceCode, record, account, existingTc.getIdTc(), nhsaAccount, now);
                existingStatusList.add(status);
            }
        }

        // 4. 批量保存新增的追溯码
        if (!newTcList.isEmpty()) {
            log.info("批量保存新增追溯码记录，数量: {}", newTcList.size());
            boolean saveResult = ysfStoTcService.saveBatch(newTcList);
            if (!saveResult) {
                throw new BusinessException("批量保存追溯码记录失败");
            }

            // 5. 更新状态记录中的idTc字段
            for (int i = 0; i < newTcList.size() && i < newStatusList.size(); i++) {
                YsfStoTc newTc = newTcList.get(i);
                YsfStoTcStatus status = newStatusList.get(i);
                status.setIdTc(newTc.getIdTc());
            }
        }

        // 6. 批量更新已存在的追溯码
        if (!updateTcList.isEmpty()) {
            log.info("批量更新已存在追溯码记录，数量: {}", updateTcList.size());
            boolean updateResult = ysfStoTcService.updateBatchById(updateTcList);
            if (!updateResult) {
                throw new BusinessException("批量更新追溯码记录失败");
            }
        }

        // 7. 批量保存状态记录
        List<YsfStoTcStatus> allStatusList = new ArrayList<>();
        allStatusList.addAll(newStatusList);
        allStatusList.addAll(existingStatusList);

        if (!allStatusList.isEmpty()) {
            log.info("批量保存追溯码状态记录，数量: {}", allStatusList.size());
            boolean statusResult = ysfStoTcStatusService.saveBatch(allStatusList);
            if (!statusResult) {
                throw new BusinessException("批量保存追溯码状态记录失败");
            }
        }

        log.info("批量处理追溯码数据完成 - 新增: {}条, 更新: {}条, 状态记录: {}条",
                newTcList.size(), updateTcList.size(), allStatusList.size());
    }

    /**
     * 批量查询已存在的追溯码
     */
    private Map<String, YsfStoTc> batchQueryExistingTraceCodes(List<String> traceCodes) {
        if (CollectionUtils.isEmpty(traceCodes)) {
            return new HashMap<>();
        }

        Map<String, YsfStoTc> resultMap = new HashMap<>();
        List<List<String>> batches = partitionList(traceCodes, BATCH_SIZE);

        for (List<String> batch : batches) {
            LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(YsfStoTc::getDrugtracinfo, batch)
                    .eq(YsfStoTc::getDelFlag, "0");

            List<YsfStoTc> existingList = ysfStoTcService.list(queryWrapper);
            Map<String, YsfStoTc> batchMap = existingList.stream()
                    .collect(Collectors.toMap(YsfStoTc::getDrugtracinfo, Function.identity(), (o, n) -> n));

            resultMap.putAll(batchMap);
        }

        return resultMap;
    }

    /**
     * 构建新的追溯码记录
     */
    private YsfStoTc buildNewTraceCode(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                       SyncAccountEnum account, NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTc newTc = new YsfStoTc();
        newTc.setDrugtracinfo(traceCode);
        newTc.setDrugCode(record.getHisDrugCode());
        newTc.setManuLotnum(record.getManuLotnum());

        // 解析有效期日期
        if (StringUtils.isNotBlank(record.getExpyEnd())) {
            try {
                LocalDate expyDate = DateUtils.parseDate(record.getExpyEnd());
                if (expyDate != null) {
                    newTc.setExpyEnd(expyDate.atStartOfDay());
                }
            } catch (Exception e) {
                log.error("解析有效期失败: {}, 错误: {}", record.getExpyEnd(), e.getMessage());
            }
        }

        // 解析生产日期
        if (StringUtils.isNotBlank(record.getManuDate())) {
            try {
                newTc.setManuDate(DateUtils.parseDateTime(record.getManuDate()));
            } catch (Exception e) {
                log.error("解析生产日期失败: {}, 错误: {}", record.getManuDate(), e.getMessage());
            }
        }

        if (StringUtils.isNotBlank(record.getHisConRatio())) {
            newTc.setUnitSaleFactor(parseDecimalToInteger(record.getHisConRatio()));
            newTc.setUnitTc(record.getHisDosUnit());
        }

        newTc.setFgActive("1"); // 有效
        newTc.setIdDept(record.getPatWardId());

        // 设置机构信息
        newTc.setIdOrg(nhsaAccount.getMedicalCode());
        newTc.setOrgId(nhsaAccount.getMedicalCode());
        newTc.setOrgName(nhsaAccount.getMedicalName());
        newTc.setSdTcManage("简易管理");

        // 审计字段
        newTc.setCreateTime(now);
        newTc.setUpdateTime(now);
        newTc.setCreateBy(account.getUsername());
        newTc.setUpdateBy(account.getUsername());
        newTc.setDelFlag("0");

        return newTc;
    }

    /**
     * 构建追溯码状态记录
     */
    private YsfStoTcStatus buildTraceCodeStatus(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                                SyncAccountEnum account, Long idTc,
                                                NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTcStatus status = new YsfStoTcStatus();

        // 基本信息
        status.setSdTcStatus(SdTcStatusEnum.INPATIENT_DISPENSING.getCode());
        status.setSdTcManage("1"); // 简易管理模式
        status.setIdBizOri(record.getRecordDetailId());
        status.setCfmxxh(record.getCfmxxh());
        status.setDrugCode(record.getHisDrugCode());
        status.setDrugtracinfo(traceCode);
        status.setSdTc("2"); // 商品追溯码
        status.setSelRetnCnt(record.getSelRetnCnt());
        status.setFgUp("0"); // 未上传
        status.setFgActive("1"); // 有效
        status.setIdDept(record.getPatWardId());
        status.setIdTc(idTc);

        // 设置机构信息
        status.setIdOrg(nhsaAccount.getMedicalCode());
        status.setOrgId(nhsaAccount.getMedicalCode());
        status.setOrgName(nhsaAccount.getMedicalName());

        // 审计字段
        status.setCreateTime(now);
        status.setUpdateTime(now);
        status.setCreateBy(account.getUsername());
        status.setUpdateBy(account.getUsername());
        status.setDelFlag("0");

        return status;
    }

    /**
     * 更新3505表中的批号、有效期、批次号信息
     */
    private void updateNhsa3505BatchInfo(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId) {
        log.info("开始处理3505表批次信息，记录数: {}", batch.size());

        int insertCount = 0;
        int skipCount = 0;
        List<Nhsa3505> saveList = new ArrayList<>();

        // 1. 批量查询已存在的cfmxxh
        List<String> cfmxxhList = batch.stream()
                .map(InPatientDispenseDetailBindScatteredVo::getCfmxxh)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        Set<String> existingCfmxxhs = new HashSet<>();
        if (!cfmxxhList.isEmpty()) {
            LambdaQueryWrapper<Nhsa3505> existingQueryWrapper = new LambdaQueryWrapper<>();
            existingQueryWrapper.in(Nhsa3505::getCfmxxh, cfmxxhList)
                    .eq(Nhsa3505::getDeleteFlag, "0")
                    .select(Nhsa3505::getCfmxxh);

            List<Nhsa3505> existingRecords = nhsa3505Service.list(existingQueryWrapper);
            existingCfmxxhs = existingRecords.stream()
                    .map(Nhsa3505::getCfmxxh)
                    .collect(Collectors.toSet());
        }

        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            try {
                // 检查必要字段
                if (StringUtils.isBlank(record.getCfmxxh())) {
                    log.warn("处方明细序号为空，跳过该条记录");
                    skipCount++;
                    continue;
                }

                // 检查是否已存在记录
                if (existingCfmxxhs.contains(record.getCfmxxh())) {
                    log.info("3505表记录已存在，跳过处理: cfmxxh={}", record.getCfmxxh());
                    skipCount++;
                    continue;
                }

                // 构建新记录
                Nhsa3505 new3505 = build3505Record(record, account, ymfUserId);
                saveList.add(new3505);

            } catch (Exception e) {
                log.error("处理3505表批次信息异常，cfmxxh: {}", record.getCfmxxh(), e);
                skipCount++;
            }
        }

        // 5. 批量保存新记录
        if (!saveList.isEmpty()) {
            boolean result = nhsa3505Service.saveBatch(saveList);
            if (result) {
                insertCount = saveList.size();
                log.info("3505表批次信息批量保存成功，新增: {}条", insertCount);
            } else {
                log.error("3505表批次信息批量保存失败");
            }
        }

        log.info("3505表批次信息处理完成 - 新增: {}条, 跳过: {}条", insertCount, skipCount);
    }

    /**
     * 构建3505记录
     */
    private Nhsa3505 build3505Record(InPatientDispenseDetailBindScatteredVo record, SyncAccountEnum account, Long ymfUserId) {
        Nhsa3505 new3505 = new Nhsa3505();

        // 设置机构信息
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        new3505.setMedicalCode(nhsaAccount.getMedicalCode());
        new3505.setMedicalName(nhsaAccount.getMedicalName());

        // 基本药品信息
        new3505.setMedListCodg(record.getMedListCodg());
        new3505.setFixmedinsHilistId(record.getFixmedinsHilistId());
        new3505.setFixmedinsHilistName(record.getFixmedinsHilistName());
        new3505.setFixmedinsBchno(record.getFixmedinsBchno());

        // 医师信息
        new3505.setPrscDrCertno(record.getPrscDrCertno());
        new3505.setPrscDrName(record.getPrscDrName());

        // 药师信息
        new3505.setPharCertno(record.getPharCertno());
        new3505.setPharName(record.getPharName());
        new3505.setPharPracCertNo(record.getPharPracCertNo());

        // 患者就诊信息
        new3505.setMdtrtSn(record.getMdtrtSn());
        new3505.setPsnName(record.getPsnName());

        // 生产信息
        new3505.setManuLotnum(record.getManuLotnum());
        if (StringUtils.isNotBlank(record.getManuDate())) {
            new3505.setManuDate(DateUtils.parseDate(record.getManuDate()));
        }
        if (StringUtils.isNotBlank(record.getExpyEnd())) {
            new3505.setExpyEnd(DateUtils.parseDate(record.getExpyEnd()));
        }
        new3505.setBchno(record.getBchno());

        // 标志信息
        new3505.setRxFlag(record.getRxFlag());
        new3505.setTrdnFlag(record.getTrdnFlag());

        // 处方信息
        new3505.setRtalDocno(record.getRtalDocno());
        new3505.setStooutNo(record.getStooutNo());
        if (!CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
            new3505.setDrugTracInfo(String.join(",", record.getDrugTracCodgs()));
        }

        // 销售信息
        new3505.setSelRetnCnt(new BigDecimal(record.getSelRetnCnt()));
        if (StringUtils.isNotBlank(record.getSelRetnTime())) {
            new3505.setSelRetnTime(DateUtils.parseDateTime(record.getSelRetnTime()));
        }
        new3505.setSelRetnOpterName(record.getPharName());
        new3505.setMdtrtSetlType(record.getMdtrtSetlType());
        new3505.setHisEntpName(record.getProdentpName());

        // 处方序号信息
        new3505.setCfxh(record.getCfxh());
        new3505.setCfmxxh(record.getCfmxxh());
        new3505.setPatientId(record.getPatInHosId());

        // 系统信息
        LocalDateTime now = LocalDateTime.now();
        new3505.setCreateTime(now);
        new3505.setCreateDate(now.toLocalDate());
        new3505.setCreateBy(account.getUsername());
        new3505.setUpdateTime(now);
        new3505.setUpdateBy(account.getUsername());
        new3505.setDeleteFlag("0");
        new3505.setHsaSyncStatus("0"); // 未同步状态

        new3505.setYmfUserName(account.getUsername());
        new3505.setYmfNickName(account.getDescription());
        new3505.setYmfUserId(ymfUserId);

        new3505.setDeptId(record.getDeptId());
        // 发药单类型
        new3505.setSdDps(SdDpsEnum.INPATIENT);

        // 设置必填字段的默认值
        setDefaultValues(new3505);

        return new3505;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDispenseDataOnlyByCredentials(List<InPatientDispenseDetailBindScatteredVo> batch, String username, String password, Long ymfUserId) {
        if (CollectionUtils.isEmpty(batch)) {
            log.info("批次数据为空，跳过保存操作");
            return;
        }

        log.info("开始保存住院发药数据到本地数据库（使用账号信息），数量: {}, 用户名: {}", batch.size(), username);

        try {
            // 创建临时的账号信息对象，用于替代SyncAccountEnum
            AccountInfo accountInfo = new AccountInfo(username, password, "住院药房");
            
            // 1. 过滤重复数据并进行校验
            List<InPatientDispenseDetailBindScatteredVo> validatedBatch = filterAndValidateBatchData(batch);
            if (CollectionUtils.isEmpty(validatedBatch)) {
                log.info("批次数据全部已处理过或校验失败，跳过后续操作");
                return;
            }

            // 2. 保存发药单主表（ysf_sto_dps）
            List<YsfStoDps> dpsList = new ArrayList<>();
            Map<String, YsfStoDps> dpsMap = new HashMap<>();

            for (InPatientDispenseDetailBindScatteredVo record : validatedBatch) {
                if (!dpsMap.containsKey(record.getCfxh())) {
                    YsfStoDps dps = buildDpsWithAccountInfo(record, accountInfo);
                    dpsList.add(dps);
                    dpsMap.put(record.getCfxh(), dps);
                }
            }

            if (!dpsList.isEmpty()) {
                ysfStoDpsService.saveBatch(dpsList);
                log.info("保存发药单主表成功，数量: {}", dpsList.size());
            }

            // 3. 保存发药单明细表（ysf_sto_dps_sub_hos）
            List<YsfStoDpsSubHos> dpsSubHosList = new ArrayList<>();

            for (InPatientDispenseDetailBindScatteredVo record : validatedBatch) {
                YsfStoDps dps = dpsMap.get(record.getCfxh());
                YsfStoDpsSubHos dpsSubHos = buildDpsSubHosWithAccountInfo(record, dps, accountInfo);
                if (dps.getIdDps() != null && dpsSubHos.getIdDps() == null) {
                    dpsSubHos.setIdDps(dps.getIdDps().toString());
                }
                dpsSubHosList.add(dpsSubHos);
            }

            if (!dpsSubHosList.isEmpty()) {
                ysfStoDpsSubHosService.saveBatch(dpsSubHosList);
                log.info("保存住院发药单明细表成功，数量: {}", dpsSubHosList.size());
            }

            // 4. 处理追溯码数据
            processTraceCodeDataWithAccountInfo(validatedBatch, accountInfo);

            // 注意：此方法不保存3505数据，如注释所述
            log.info("跳过3505表数据保存（使用凭据的方法不保存3505数据）");

            log.info("成功保存住院发药数据到本地数据库，数量: {}", validatedBatch.size());

        } catch (Exception e) {
            log.error("保存住院发药数据失败", e);
            throw new BusinessException("保存住院发药数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建发药单对象（使用AccountInfo）
     */
    private YsfStoDps buildDpsWithAccountInfo(InPatientDispenseDetailBindScatteredVo record, AccountInfo accountInfo) {
        YsfStoDps dps = new YsfStoDps();

        // 基本信息
        dps.setCfxh(record.getCfxh());
        dps.setSdDps(SdDpsEnum.INPATIENT); // 1:住院
        dps.setPatientId(record.getPatInHosId());
        dps.setIdPi(record.getPatInHosId());
        dps.setPsnName(record.getPsnName());
        dps.setPatInHosId(record.getPatInHosId());
        dps.setPatWardId(record.getPatWardId());
        dps.setPatWardName(record.getPatWardName());

        // 发药信息
        if (StringUtils.isNotBlank(record.getSendTime())) {
            dps.setSendTime(DateUtils.parseDateTime(record.getSendTime()));
        }
        dps.setIdDept(record.getDeptId());
        dps.setSelRetnOpterName(record.getPharName());
        dps.setSelRetnOpterId(record.getPharPracCertNo());

        // 状态信息
        dps.setFgStatus(DispenseOrderStatusEnum.DISPENSED.getCode()); // 已发药
        dps.setFgDps("0"); // 0:发药
        dps.setFgPrint("0"); // 未打印

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dps.setIdOrg(nhsaAccount.getMedicalCode());
        dps.setOrgId(nhsaAccount.getMedicalCode());
        dps.setOrgName(nhsaAccount.getMedicalName());
        dps.setCreateTime(LocalDateTime.now());
        dps.setUpdateTime(LocalDateTime.now());
        dps.setCreateBy(accountInfo.getUsername());
        dps.setUpdateBy(accountInfo.getUsername());
        dps.setDelFlag("0");

        return dps;
    }

    /**
     * 构建发药单明细对象（使用AccountInfo）
     */
    private YsfStoDpsSubHos buildDpsSubHosWithAccountInfo(InPatientDispenseDetailBindScatteredVo record, YsfStoDps dps, AccountInfo accountInfo) {
        YsfStoDpsSubHos dpsSubHos = new YsfStoDpsSubHos();

        // 关联信息
        dpsSubHos.setCfmxxh(record.getCfmxxh());
        dpsSubHos.setCfxh(record.getCfxh());
        if (dps.getIdDps() != null) {
            dpsSubHos.setIdDps(dps.getIdDps().toString());
        }
        dpsSubHos.setIdFee(record.getIdFee());
        dpsSubHos.setNaFee(record.getNaFee());

        // 药品信息
        dpsSubHos.setDrugCode(record.getFixmedinsHilistId());

        // 数量信息
        dpsSubHos.setSelRetnCnt(record.getSelRetnCnt());
        dpsSubHos.setQuantity(record.getSelRetnCnt());
        dpsSubHos.setUnit(record.getSelRetnUnit());
        dpsSubHos.setUnitSale(record.getSelRetnUnit());

        // 追溯码信息
        if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
            dpsSubHos.setDrugtracinfo(String.join(",", record.getDrugTracCodgs()));
            dpsSubHos.setTracCnt(record.getDrugTracCodgs().size());
        } else {
            dpsSubHos.setTracCnt(0);
        }

        // 患者信息
        dpsSubHos.setPatInHosId(record.getPatInHosId());
        dpsSubHos.setPatientId(record.getPatInHosId());
        dpsSubHos.setPsnName(record.getPsnName());

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dpsSubHos.setIdOrg(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgId(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgName(nhsaAccount.getMedicalName());
        dpsSubHos.setCreateTime(LocalDateTime.now());
        dpsSubHos.setUpdateTime(LocalDateTime.now());
        dpsSubHos.setCreateBy(accountInfo.getUsername());
        dpsSubHos.setUpdateBy(accountInfo.getUsername());
        dpsSubHos.setDelFlag("0");

        return dpsSubHos;
    }

    /**
     * 处理追溯码数据（使用AccountInfo）
     */
    private void processTraceCodeDataWithAccountInfo(List<InPatientDispenseDetailBindScatteredVo> batch, AccountInfo accountInfo) {
        log.info("开始批量处理追溯码数据，记录数: {}", batch.size());

        // 1. 收集所有需要处理的追溯码信息
        List<TraceCodeProcessInfo> traceCodeInfos = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
                for (String traceCode : record.getDrugTracCodgs()) {
                    TraceCodeProcessInfo info = new TraceCodeProcessInfo();
                    info.setTraceCode(traceCode);
                    info.setRecord(record);
                    traceCodeInfos.add(info);
                }
            }
        }

        if (traceCodeInfos.isEmpty()) {
            log.info("没有需要处理的追溯码数据");
            return;
        }

        log.info("需要处理的追溯码总数: {}", traceCodeInfos.size());

        // 2. 批量查询已存在的追溯码
        List<String> allTraceCodes = traceCodeInfos.stream()
                .map(TraceCodeProcessInfo::getTraceCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, YsfStoTc> existingTcMap = batchQueryExistingTraceCodes(allTraceCodes);

        // 3. 分离新增和更新的追溯码
        List<YsfStoTc> newTcList = new ArrayList<>();
        List<YsfStoTc> updateTcList = new ArrayList<>();
        List<YsfStoTcStatus> newStatusList = new ArrayList<>();
        List<YsfStoTcStatus> existingStatusList = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

        for (TraceCodeProcessInfo info : traceCodeInfos) {
            String traceCode = info.getTraceCode();
            InPatientDispenseDetailBindScatteredVo record = info.getRecord();

            YsfStoTc existingTc = existingTcMap.get(traceCode);

            if (existingTc == null) {
                // 创建新的追溯码记录
                YsfStoTc newTc = buildNewTraceCodeWithAccountInfo(traceCode, record, accountInfo, nhsaAccount, now);
                newTcList.add(newTc);

                // 创建对应的状态记录
                YsfStoTcStatus status = buildTraceCodeStatusWithAccountInfo(traceCode, record, accountInfo, null, nhsaAccount, now);
                newStatusList.add(status);
            } else {
                // 更新已存在的追溯码
                existingTc.setUpdateTime(now);
                existingTc.setUpdateBy(accountInfo.getUsername());
                updateTcList.add(existingTc);

                // 创建状态记录
                YsfStoTcStatus status = buildTraceCodeStatusWithAccountInfo(traceCode, record, accountInfo, existingTc.getIdTc(), nhsaAccount, now);
                existingStatusList.add(status);
            }
        }

        // 4. 批量保存新增的追溯码
        if (!newTcList.isEmpty()) {
            log.info("批量保存新增追溯码记录，数量: {}", newTcList.size());
            boolean saveResult = ysfStoTcService.saveBatch(newTcList);
            if (!saveResult) {
                throw new BusinessException("批量保存追溯码记录失败");
            }

            // 5. 更新状态记录中的idTc字段
            for (int i = 0; i < newTcList.size() && i < newStatusList.size(); i++) {
                YsfStoTc newTc = newTcList.get(i);
                YsfStoTcStatus status = newStatusList.get(i);
                status.setIdTc(newTc.getIdTc());
            }
        }

        // 6. 批量更新已存在的追溯码
        if (!updateTcList.isEmpty()) {
            log.info("批量更新已存在追溯码记录，数量: {}", updateTcList.size());
            boolean updateResult = ysfStoTcService.updateBatchById(updateTcList);
            if (!updateResult) {
                throw new BusinessException("批量更新追溯码记录失败");
            }
        }

        // 7. 批量保存状态记录
        List<YsfStoTcStatus> allStatusList = new ArrayList<>();
        allStatusList.addAll(newStatusList);
        allStatusList.addAll(existingStatusList);

        if (!allStatusList.isEmpty()) {
            log.info("批量保存追溯码状态记录，数量: {}", allStatusList.size());
            boolean statusResult = ysfStoTcStatusService.saveBatch(allStatusList);
            if (!statusResult) {
                throw new BusinessException("批量保存追溯码状态记录失败");
            }
        }

        log.info("批量处理追溯码数据完成 - 新增: {}条, 更新: {}条, 状态记录: {}条",
                newTcList.size(), updateTcList.size(), allStatusList.size());
    }

    /**
     * 构建新的追溯码记录（使用AccountInfo）
     */
    private YsfStoTc buildNewTraceCodeWithAccountInfo(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                       AccountInfo accountInfo, NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTc newTc = new YsfStoTc();
        newTc.setDrugtracinfo(traceCode);
        newTc.setDrugCode(record.getHisDrugCode());
        newTc.setManuLotnum(record.getManuLotnum());

        // 解析有效期日期
        if (StringUtils.isNotBlank(record.getExpyEnd())) {
            try {
                LocalDate expyDate = DateUtils.parseDate(record.getExpyEnd());
                if (expyDate != null) {
                    newTc.setExpyEnd(expyDate.atStartOfDay());
                }
            } catch (Exception e) {
                log.error("解析有效期失败: {}, 错误: {}", record.getExpyEnd(), e.getMessage());
            }
        }

        // 解析生产日期
        if (StringUtils.isNotBlank(record.getManuDate())) {
            try {
                newTc.setManuDate(DateUtils.parseDateTime(record.getManuDate()));
            } catch (Exception e) {
                log.error("解析生产日期失败: {}, 错误: {}", record.getManuDate(), e.getMessage());
            }
        }

        if (StringUtils.isNotBlank(record.getHisConRatio())) {
            newTc.setUnitSaleFactor(parseDecimalToInteger(record.getHisConRatio()));
            newTc.setUnitTc(record.getHisDosUnit());
        }

        newTc.setFgActive("1"); // 有效
        newTc.setIdDept(record.getPatWardId());

        // 设置机构信息
        newTc.setIdOrg(nhsaAccount.getMedicalCode());
        newTc.setOrgId(nhsaAccount.getMedicalCode());
        newTc.setOrgName(nhsaAccount.getMedicalName());
        newTc.setSdTcManage("简易管理");

        // 审计字段
        newTc.setCreateTime(now);
        newTc.setUpdateTime(now);
        newTc.setCreateBy(accountInfo.getUsername());
        newTc.setUpdateBy(accountInfo.getUsername());
        newTc.setDelFlag("0");

        return newTc;
    }

    /**
     * 构建追溯码状态记录（使用AccountInfo）
     */
    private YsfStoTcStatus buildTraceCodeStatusWithAccountInfo(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                                AccountInfo accountInfo, Long idTc,
                                                NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTcStatus status = new YsfStoTcStatus();

        // 基本信息
        status.setSdTcStatus(SdTcStatusEnum.INPATIENT_DISPENSING.getCode());
        status.setSdTcManage("1"); // 简易管理模式
        status.setIdBizOri(record.getRecordDetailId());
        status.setCfmxxh(record.getCfmxxh());
        status.setDrugCode(record.getHisDrugCode());
        status.setDrugtracinfo(traceCode);
        status.setSdTc("2"); // 商品追溯码
        status.setSelRetnCnt(record.getSelRetnCnt());
        status.setFgUp("0"); // 未上传
        status.setFgActive("1"); // 有效
        status.setIdDept(record.getPatWardId());
        status.setIdTc(idTc);

        // 设置机构信息
        status.setIdOrg(nhsaAccount.getMedicalCode());
        status.setOrgId(nhsaAccount.getMedicalCode());
        status.setOrgName(nhsaAccount.getMedicalName());

        // 审计字段
        status.setCreateTime(now);
        status.setUpdateTime(now);
        status.setCreateBy(accountInfo.getUsername());
        status.setUpdateBy(accountInfo.getUsername());
        status.setDelFlag("0");

        return status;
    }

    /**
     * 账号信息内部类，用于替代SyncAccountEnum
     */
    @Data
    private static class AccountInfo {
        private final String username;
        private final String password;
        private final String description;

        public AccountInfo(String username, String password, String description) {
            this.username = username;
            this.password = password;
            this.description = description;
        }
    }

    /**
     * 设置3505记录的默认值
     */
    private void setDefaultValues(Nhsa3505 nhsa3505) {
        if (!StringUtils.isNotBlank(nhsa3505.getPrscDrName())) {
            nhsa3505.setPrscDrName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getPharName())) {
            nhsa3505.setPharName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getPsnName())) {
            nhsa3505.setPsnName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getSelRetnOpterName())) {
            nhsa3505.setSelRetnOpterName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getHisUniqueKey())) {
            nhsa3505.setHisUniqueKey(nhsa3505.getFixmedinsBchno());
        }
        if (!StringUtils.isNotBlank(nhsa3505.getCompositeKey())) {
            nhsa3505.setCompositeKey(nhsa3505.getFixmedinsBchno());
        }
    }

    /**
     * 将列表按指定大小分割成多个子列表
     */
    private static <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int size = list.size();

        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            batches.add(list.subList(i, endIndex));
        }

        return batches;
    }

    /**
     * 安全地将小数字符串转换为整数
     * 
     * @param decimalString 可能包含小数的字符串，如 "5.0000"
     * @return 转换后的整数值，如果转换失败则返回1作为默认值
     */
    private Integer parseDecimalToInteger(String decimalString) {
        if (StringUtils.isBlank(decimalString)) {
            return 1; // 默认值
        }
        
        try {
            // 先转换为 BigDecimal 以处理小数，然后转换为整数
            java.math.BigDecimal decimal = new java.math.BigDecimal(decimalString.trim());
            return decimal.intValue();
        } catch (NumberFormatException e) {
            log.warn("无法解析转换比字符串 '{}' 为整数，使用默认值 1", decimalString);
            return 1; // 默认值
        }
    }

    /**
     * 追溯码处理信息内部类
     */
    @Data
    private static class TraceCodeProcessInfo {
        private String traceCode;
        private InPatientDispenseDetailBindScatteredVo record;
    }
}