package com.zsm.controller;

import cn.hutool.core.date.DateUtil;
import com.zsm.common.SaasAuthorizationVerify;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.domain.DateRange;
import com.zsm.model.dto.*;
import com.zsm.model.vo.*;
import com.zsm.service.*;
import com.zsm.utils.DateRangeUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 安医阜阳 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Tag(name = "安医阜阳", description = "门诊发药业务接口")
@RestController
@RequestMapping("/anYiFuYang")
public class AnYiFuYangController {

    @Resource
    private HangChuangService hangChuangService;
    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;
    @Resource
    private YsfStoTcService ysfStoTcService;
    @Resource
    private DispensingService dispensingService;
    @Resource
    private YsfService ysfService;
    @Resource
    private ReturnDrugService returnDrugService;
    @Resource
    private Nhsa3505Service nhsa3505Service;

    /**
     * 查询门诊处方接口（默认查询）
     *
     * @return 门诊处方数据
     */
    @PostMapping("/queryOutpatientPrescription")
    @Operation(summary = "查询门诊处方")
    @SaasAuthorizationVerify
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(@Validated @RequestBody OutpatientPrescriptionQueryDto queryDto) {
        return hangChuangService.queryOutpatientPrescription(queryDto);
    }

    /**
     * 住院发药记录接口
     *
     * @param queryDto 查询参数
     * @return 住院发药记录数据
     */
    @PostMapping("/queryInpatientDispenseRecord")
    @Operation(summary = "1.住院发药记录接口")
    public ApiResult<List<InPatientDispenseRecordVo>> queryInpatientDispenseRecord(@RequestBody InpatientPrescriptionQueryDto queryDto) {
        return hangChuangService.queryInpatientDispenseRecord(queryDto);
    }

    /**
     * 住院领药单接口
     *
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    @PostMapping("/queryInpatientPrescription")
    @Operation(summary = "2.住院领药单接口")
    @SaasAuthorizationVerify
    public ApiResult<List<InPatientDispenseDetailBindScatteredVo>> queryInpatientPrescription(@Validated @RequestBody InpatientPrescriptionQueryDto queryDto) {
        return hangChuangService.queryInpatientPrescription(queryDto);
    }

    /**
     * 药品追溯码扫描与上传接口
     *
     * @param request 追溯码上传请求
     * @return 上传结果
     */
    @PostMapping("/uploadScans")
    @Operation(summary = "药品追溯码扫描上传", description = "将本次采集的所有药品追溯码信息批量提交")
    @SaasAuthorizationVerify
    public ApiResult<TraceabilityUploadResultVo> uploadScans(@RequestBody TraceabilityUploadDto request) {
        // 调用服务处理追溯码上传
        return ysfService.uploadScans(request);
    }

    /**
     * 取消扫码任务
     *
     * @param taskId 任务ID
     * @return 取消结果
     */
    @PostMapping("/cancel/{taskId}")
    @Operation(summary = "取消扫码任务", description = "根据任务ID取消未完成的扫码任务")
    @SaasAuthorizationVerify
    public ApiResult<String> cancelTask(@PathVariable Long taskId) {
        return ysfService.cancelTask(taskId);
    }

    /**
     * 查询发药单列表
     *
     * @param queryDto 查询参数
     * @return 发药单列表
     */
    @GetMapping("/YsfStoDps/list")
    @Operation(summary = "查询发药单列表", description = "根据条件查询发药单列表信息，包含分页")
    @SaasAuthorizationVerify
    public TableDataInfo listDispensingRecords(DispensingRecordQueryDto queryDto) {
        return ysfService.queryDispensingRecords(queryDto);
    }

    /**
     * 查询发药单明细列表
     *
     * @param queryDto 查询参数
     * @return 发药单明细列表
     */
    @GetMapping("/YsfStoDpsSub/list")
    @Operation(summary = "查询发药单明细列表", description = "根据发药单ID查询发药单明细列表，支持按药品名称/编码、是否已采集等条件过滤")
    @SaasAuthorizationVerify
    public TableDataInfo listDispensingRecordDetails(DispensingRecordDetailQueryDto queryDto) {
        return ysfService.queryDispensingRecordDetails(queryDto);
    }

    /**
     * 查询扫码任务列表
     *
     * @param queryDto 查询参数
     * @return 扫码任务列表
     */
    @GetMapping("/YsfStoTcTask/list")
    @Operation(summary = "查询扫码任务列表", description = "支持按任务状态、患者姓名、业务单号、任务类型、创建时间范围等查询")
    @SaasAuthorizationVerify
    public TableDataInfo listTasks(@Validated YsfStoTcTaskQueryDto queryDto) {
        return ysfStoTcTaskService.queryTaskList(queryDto);
    }

    /**
     * 查询扫码任务明细列表
     *
     * @param queryDto 查询参数
     * @return 扫码任务明细列表
     */
    @GetMapping("/YsfStoTcTaskSub/list")
    @Operation(summary = "查询扫码任务明细列表", description = "根据任务ID查询扫码任务明细列表，支持按药品名称/编码、是否已扫码等条件过滤")
    @SaasAuthorizationVerify
    public TableDataInfo listTaskDetails(@Validated YsfStoTcTaskSubQueryDto queryDto) {
        return ysfStoTcTaskSubService.queryTaskSubList(queryDto);
    }

    /**
     * 单个追溯码及其生命周期查询
     *
     * @param drugtracinfo 追溯码
     * @return 追溯码信息和生命周期记录
     */
    @GetMapping("/YsfStoTc/getByDrugtracinfo/{drugtracinfo}")
    @Operation(summary = "查询单个追溯码及其生命周期", description = "根据追溯码查询其基本信息和状态变更历史")
    @SaasAuthorizationVerify
    public ApiResult<TraceabilityCodeInfoVo> getByDrugtracinfo(@PathVariable("drugtracinfo") String drugtracinfo) {
        // 调用服务查询追溯码信息
        TraceabilityCodeInfoVo result = ysfStoTcService.getByDrugtracinfo(drugtracinfo);
        if (result.getTraceabilityCodeInfo() == null) {
            return ApiResult.error("未找到该追溯码信息");
        }
        return ApiResult.success(result);
    }

    /**
     * 查询追溯码流转记录列表
     *
     * @param queryDto 查询参数
     * @return 流转记录列表
     */
    @GetMapping("/YsfStoTcStatus/list")
    @Operation(summary = "查询追溯码流转记录", description = "根据条件查询追溯码流转记录列表")
    @SaasAuthorizationVerify
    public TableDataInfo listTcStatusRecords(@Validated YsfStoTcStatusQueryDto queryDto) {
        return ysfStoTcStatusService.queryStatusRecordList(queryDto);
    }

    /**
     * 根据业务子ID查询发药明细
     *
     * @param bizSubId 业务子ID，通常是发药单明细ID或处方明细ID
     * @return 发药明细信息
     */
    @GetMapping("/dispensing/detailsByBizSubId")
    @Operation(summary = "根据业务子ID查询发药明细", description = "通过业务子ID（如发药单明细ID）直接查询关联的发药信息")
    @SaasAuthorizationVerify
    public ApiResult<DispensingDetailVo> getDispensingDetailsByBizSubId(@RequestParam("bizSubId") String bizSubId) {
        // 调用服务获取发药明细
        DispensingDetailVo detail = dispensingService.getDetailByBizSubId(bizSubId);
        if (detail == null) {
            return ApiResult.error("未找到相关发药明细信息");
        }
        return ApiResult.success(detail);
    }

    /**
     * 退药上传
     *
     * @param uploadDto 退药上传数据
     * @return 退药上传结果
     */
    @PostMapping("/return")
    @Operation(summary = "退药上传", description = "上传退药信息进行退药处理")
    @SaasAuthorizationVerify
    public ApiResult<ReturnDrugResultVo> returnDrug(@RequestBody @Validated TraceabilityUploadDto uploadDto) {
        return returnDrugService.returnDrug(uploadDto);
    }

    /**
     * 同步药品字典接口
     *
     * @return 同步结果
     */
    @PostMapping("/syncDrugDictionary")
    @Operation(summary = "同步his药品字典", description = "从HIS系统同步药品字典数据到本地数据库")
    public ApiResult<String> syncDrugDictionary() {
        return hangChuangService.syncDrugDictionary();
    }

}
