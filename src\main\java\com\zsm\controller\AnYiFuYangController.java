package com.zsm.controller;

import com.zsm.common.SaasAuthorizationVerify;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.*;
import com.zsm.model.vo.*;
import com.zsm.service.*;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * <p>
 * 安医阜阳 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Tag(name = "安医阜阳", description = "门诊发药业务接口")
@RestController
@RequestMapping("/anYiFuYang")
public class AnYiFuYangController {

    @Resource
    private XiaMenZhiYeService xiaMenZhiYeService;

    /**
     * 查询门诊处方接口（默认查询）
     *
     * @return 门诊处方数据
     */
    @PostMapping("/queryOutpatientPrescription")
    @Operation(summary = "查询门诊处方")
    @SaasAuthorizationVerify
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(@Validated @RequestBody OutpatientPrescriptionQueryDto queryDto) {
        return xiaMenZhiYeService.queryOutpatientPrescription(queryDto);
    }

    /**
     * 住院领药单接口
     *
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    @PostMapping("/queryInpatientPrescription")
    @Operation(summary = "住院领药单接口")
    @SaasAuthorizationVerify
    public ApiResult<List<InPatientDispenseDetailBindScatteredVo>> queryInpatientPrescription(@Validated @RequestBody InpatientPrescriptionQueryDto queryDto) {
        return xiaMenZhiYeService.queryInpatientPrescription(queryDto);
    }


    /**
     * 同步药品字典接口
     *
     * @return 同步结果
     */
    @PostMapping("/syncDrugDictionary")
    @Operation(summary = "同步his药品字典", description = "从HIS系统同步药品字典数据到本地数据库")
    public ApiResult<String> syncDrugDictionary() {
        return xiaMenZhiYeService.syncDrugDictionary();
    }

}
