## 背景与目标

- **背景**: 当前 `HangChuangService#processWeeklyInpatientTraceability(startDate, endDate)` 顺序处理住院患者清单，单线程串行执行 `processInpatientWithWeeklyRange(...)`，任务在高峰期耗时较长、易受单点异常影响。
- **目标**: 基于 Redis 构建一个“简易 MQ”，将“患者级”处理拆分为消息，异步并发消费，显著提升吞吐、降低任务耦合，具备幂等、重试、限流与观测能力。

## 方案概述

- **选型**: 采用 Redis List + `RPOPLPUSH/BRPOPLPUSH` 构建可靠队列；未来可平滑升级到 Redis Streams（`XADD/XREADGROUP`）。
- **粒度**: 以“患者+周期”为一条消息（message），在 Redis 中排队。
- **处理模型**: 生产者（Producer）负责入队；多个消费者（Consumer）并发拉取、处理、确认（ack）。失败消息按退避策略重试，超过阈值进入死信队列（DLQ）。

## 关键设计

### 1) Redis Key 规范

- 业务前缀: `ysf:trace:inpatient:`
- 队列:
  - 主队列（待处理）: `ysf:trace:inpatient:queue`
  - 处理中队列: `ysf:trace:inpatient:processing`
  - 死信队列: `ysf:trace:inpatient:dlq`
- 幂等/去重:
  - 生产幂等键: `ysf:trace:inpatient:msg:{patInHosId}:{start}:{end}`（`SETNX` + TTL）
  - 处理互斥锁: `ysf:trace:inpatient:lock:{patInHosId}`（`SETNX` + TTL，避免同一患者并发处理）
- 监控/运维:
  - 开关: `ysf:trace:inpatient:switch`（`on/off`）
  - 指标: `ysf:trace:inpatient:metrics:{date}`（hash 累加 produced/consumed/failed/retried/ackTimeMs 等）

### 2) 消息结构（JSON）

```json
{
  "traceId": "uuid",
  "patInHosId": "住院号",
  "patientName": "张三",
  "startDate": "yyyy-MM-dd",
  "endDate": "yyyy-MM-dd",
  "retry": 0,
  "maxRetry": 5,
  "createdAt": 1719999999999,
  "bizMeta": {
    "mdtrtId": "就诊ID(如有)",
    "ward": "病区(可选)"
  }
}
```

说明:
- 以 `patInHosId + startDate + endDate` 作为业务主键。
- `retry` 用于记录当前重试次数；`maxRetry` 控制进入 DLQ 的阈值。

### 3) 生产者流程（替换顺序处理 → 入队）

在 `HangChuangService#processWeeklyInpatientTraceability` 中:
- 查询时间范围内患者列表。
- 对每位患者构建消息，使用 `SETNX` 做生产幂等（已入队的患者+周期不重复入队）。
- 使用 `LPUSH` 入 `ysf:trace:inpatient:queue`，快速返回“入队统计”。

伪代码（与现有 `RedisTemplate`/`RedisUtil` 适配）：

```java
String dedupKey = String.format("ysf:trace:inpatient:msg:%s:%s:%s", patInHosId, startDate, endDate);
boolean firstSeen = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(dedupKey, 1, Duration.ofDays(7)));
if (firstSeen) {
    String msg = objectMapper.writeValueAsString(message);
    redisTemplate.opsForList().leftPush("ysf:trace:inpatient:queue", msg);
}
```

返回值建议：`ApiResult.success("入队N条，跳过M条（已存在）")`。

### 4) 消费者流程（并发消费 + 可靠处理）

消费核心采用 `BRPOPLPUSH main -> processing`：
- 阻塞弹出主队列尾元素并原子地推入 `processing`，防止消费丢失。
- 处理成功后，从 `processing` 中按值 `LREM` 删除作为 ACK。
- 失败时按退避策略重试；超过阈值则移入 `dlq` 并记录异常详情。

伪代码（单消费者循环；建议配合线程池开启多实例）：

```java
while (switchOn) {
  String raw = redisTemplate.opsForList().rightPopAndLeftPush("ysf:trace:inpatient:queue", "ysf:trace:inpatient:processing", 5, TimeUnit.SECONDS);
  if (raw == null) continue;
  Message msg = parse(raw);

  String lockKey = String.format("ysf:trace:inpatient:lock:%s", msg.getPatInHosId());
  Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, 1, Duration.ofMinutes(30));
  if (Boolean.FALSE.equals(locked)) {
      // 正在处理同一患者，放回队列尾部以避免饥饿
      redisTemplate.opsForList().rightPopAndLeftPush("ysf:trace:inpatient:processing", "ysf:trace:inpatient:queue");
      continue;
  }

  long start = System.currentTimeMillis();
  try {
      boolean ok = processInpatientWithWeeklyRange(msg.toPatient(), new DateRange(msg.getStartDate(), msg.getEndDate()));
      // ack: 从processing删除该消息
      redisTemplate.opsForList().remove("ysf:trace:inpatient:processing", 1, raw);
      metrics.incConsumed(ok);
  } catch (Exception ex) {
      int retry = msg.getRetry() + 1;
      if (retry <= msg.getMaxRetry()) {
          msg.setRetry(retry);
          long backoffMs = computeBackoff(retry); // e.g. 2^retry * base
          Thread.sleep(backoffMs);
          // 重新入主队列并从processing删除
          redisTemplate.opsForList().leftPush("ysf:trace:inpatient:queue", toJson(msg));
          redisTemplate.opsForList().remove("ysf:trace:inpatient:processing", 1, raw);
          metrics.incRetried();
      } else {
          // 进入 DLQ 并从processing删除
          redisTemplate.opsForList().leftPush("ysf:trace:inpatient:dlq", rawWithError(ex));
          redisTemplate.opsForList().remove("ysf:trace:inpatient:processing", 1, raw);
          metrics.incFailed();
      }
  } finally {
      redisTemplate.delete(lockKey);
      metrics.observeAckTime(System.currentTimeMillis() - start);
  }
}
```

并发建议：
- 通过 `ThreadPoolTaskExecutor` 配置 4~8 个消费者线程，或多实例部署水平扩容。
- 对单患者串行保障由分布式锁实现；对全局吞吐由线程池/实例数控制。

### 5) 幂等、重试与限流

- **生产幂等**: `SETNX dedupKey`，7 天 TTL；确保同一患者+周期只入队一次。
- **消费互斥**: 患者级 `lock:{patInHosId}`，处理窗口 30 分钟（可按处理时长调整）。
- **重试策略**: 指数退避（如 `base=3s`, `max=5min`），`maxRetry=5` 后进入 DLQ。
- **限流**: 
  - 线程池最大并发；
  - 全局开关 `ysf:trace:inpatient:switch = off` 可快速停机；
  - 通过队列长度 `LLEN` 做自适应暂停（队列过长→适度降速）。

### 6) 观测与告警

- 指标（写入 Redis Hash 或日志采集）:
  - `produced`、`consumed`、`failed`、`retried`、`avgAckMs`、`p95AckMs`、`queueLen`。
- 日志关联 `traceId/patInHosId`；DLQ 记录异常栈与业务关键信息。
- 队列/处理时延超阈触发告警（如 15 分钟未清空）。

### 7) 资源与内存管理

- 队列消息尽量保持精简（避免冗大字段），仅携带处理所需主键与时间范围。
- `processing` 长时间滞留的消息可由“看门狗”任务回收：超过 30 分钟自动移回主队列并增加 `retry`。
- 去重键与锁键设置合理 TTL，防止泄漏。

## 集成落地步骤

> 以下为与现有工程风格最小侵入的落地指南（示例代码基于 `RedisTemplate<String, Object>`）

1) 在 `RedisUtil` 或新建 `RedisQueueUtil` 增加所需方法（可选）

```java
public Long lpush(String key, String value) { return redisTemplate.opsForList().leftPush(key, value); }
public String brpoplpush(String source, String dest, long timeout, TimeUnit unit) { return redisTemplate.opsForList().rightPopAndLeftPush(source, dest, timeout, unit); }
public Long lrem(String key, long count, String value) { return redisTemplate.opsForList().remove(key, count, value); }
public Boolean setIfAbsent(String key, Object val, Duration ttl) { return redisTemplate.opsForValue().setIfAbsent(key, val, ttl); }
public Long llen(String key) { return redisTemplate.opsForList().size(key); }
```

2) 改造 `HangChuangService#processWeeklyInpatientTraceability`

- 由“循环处理患者”改为“循环入队消息”，快速返回入队统计；原有 `processInpatientWithWeeklyRange(...)` 不动，供消费者调用。

3) 新增消费者 `InpatientTraceabilityConsumer`

- 使用 `@Component` + `@PostConstruct` 启动固定大小线程池，循环 `BRPOPLPUSH` 消费；或 `@Scheduled(fixedDelay=... , initialDelay=...)` 周期拉取直到队列为空。
- 处理流程遵循上文伪代码，含分布式锁、ACK、重试、DLQ、指标埋点。

4) 运维与回滚

- 开关 `ysf:trace:inpatient:switch` 控制消费者启停；遇紧急问题可立刻 `set switch off`。
- 需要回滚时，仅将 `processWeeklyInpatientTraceability` 切回旧实现并停用消费者；队列残留数据可回灌或清空。

## 运维常用命令（示例）

```bash
# 查看主队列长度
LLEN ysf:trace:inpatient:queue

# 查看处理中队列长度
LLEN ysf:trace:inpatient:processing

# 查看死信队列长度
LLEN ysf:trace:inpatient:dlq

# 从 DLQ 回灌一条到主队列（谨慎使用）
RPOPLPUSH ysf:trace:inpatient:dlq ysf:trace:inpatient:queue

# 紧急停机
SET ysf:trace:inpatient:switch off

# 启动消费
SET ysf:trace:inpatient:switch on
```

## 风险与权衡

- List 方案不自带“消费组”与“历史游标”，但足以覆盖本业务；如需审计/重放，建议升级 Redis Streams。
- `processing` + `RPOPLPUSH` 需要显式 ACK（`LREM`），代码要严格保证异常分支也能清理；结合“看门狗”回收降低卡死风险。
- 分布式锁 TTL 需大于最大单条处理时长；若处理时间波动大，可在关键阶段续租或拆分事务。

## 可选：升级到 Redis Streams（备选方案提要）

- 使用 `XADD ysf:trace:inpatient:stream * field value ...` 入队。
- 创建消费组 `XGROUP CREATE ...`，消费者使用 `XREADGROUP GROUP ...` 拉取并 `XACK` 确认，支持 PEL 与重传。
- 边车任务定期 `XPENDING` + `XCLAIM` 处理长时间未确认的消息。

## 与现有任务编排的关系

- `TaiHeRenYiTask#processWeeklyInpatientTraceabilityTask` 继续按 3 小时调度即可：定时“入队”；
- 新增常驻消费者或短周期拉取的消费者，持续消化队列，提升整体吞吐与稳定性。

## 验收标准（建议）

- 单次入队 ≥ 1000 名患者时，总耗时较串行版本下降 60%+。
- 无重复处理（幂等、锁生效），失败可观测且可回放（DLQ）。
- 宕机/重启不丢消息（`processing` 回收或重试生效）。

---

如需，我可以按本文方案直接补充 `RedisUtil` 的 List/SetNX 能力、改造 `processWeeklyInpatientTraceability` 为生产者，并新增 `InpatientTraceabilityConsumer`，保持对现有 `processInpatientWithWeeklyRange(...)` 的零改动复用。


