package com.zsm.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.entity.YsfDmhLayRecordsMz;
import com.zsm.entity.YsfDmhLayRecordsZy;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.saas.request.GetTracCodgStoreDataRequest;
import com.zsm.model.saas.request.GetTracCodgStoreRequest;
import com.zsm.model.saas.request.QueryTracDrugRequest;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.saas.response.QueryTracDrugResponse;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.utils.SaasHttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 厦门智业视图业务处理服务类
 * 基于 oracle_his 视图 V_YSF_DMH_LAY_RECORDS_MZ、V_YSF_DMH_LAY_RECORDS_ZY 实现
 * 与 HangChuangService 方法签名和处理流程保持一致
 */
@Slf4j
@Service
public class XiaMenZhiYeService {

    @Resource
    private YsfDmhLayRecordsMzService ysfDmhLayRecordsMzService;
    @Resource
    private YsfDmhLayRecordsZyService ysfDmhLayRecordsZyService;
    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private HangChuangService hangChuangService; // 仅用于委托药典同步

    /**
     * 按视图查询门诊处方
     *
     * @param queryDto 查询dto
     * @return {@link List }<{@link YsfDmhLayRecordsMz }>
     */
    @DS("oracle_his")
    public List<YsfDmhLayRecordsMz> queryOutpatientPrescriptionByView(OutpatientPrescriptionQueryDto queryDto) {
        if (queryDto == null) {
            queryDto = new OutpatientPrescriptionQueryDto();
        }

        validateOutpatientQueryParams(queryDto);

        // 构建查询条件
        LambdaQueryWrapper<YsfDmhLayRecordsMz> qw = new LambdaQueryWrapper<>();

        switch (queryDto.getCardType()) {
            case "1": // patient_id
                if (StrUtil.isNotBlank(queryDto.getPatient_id())) {
                    qw.eq(YsfDmhLayRecordsMz::getPatientId, queryDto.getPatient_id());
                }
                break;
            case "4": // cfxh
                if (StrUtil.isNotBlank(queryDto.getCfxh())) {
                    qw.eq(YsfDmhLayRecordsMz::getCfxh, queryDto.getCfxh());
                }
                break;
            case "5":
                // 多cfxh查询，通过逗号分割
                if (StrUtil.isNotBlank(queryDto.getCfxh())) {
                    String[] cfxhArray = queryDto.getCfxh().split(",");
                    List<String> cfxhList = Arrays.stream(cfxhArray)
                            .map(String::trim)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.toList());
                    if (!cfxhList.isEmpty()) {
                        qw.in(YsfDmhLayRecordsMz::getCfxh, cfxhList);
                    }
                }
                break;
            case "2":
            case "3":
                // 视图不包含卡号/身份证字段，无法直接查询
                throw new BusinessException("视图不支持通过卡号/身份证查询，请使用patient_id或cfxh");
            default:
                throw new BusinessException("不支持的查询类型: " + queryDto.getCardType());
        }

        // 时间范围（使用视图的 SEL_RETN_TIME）
        if (StrUtil.isNotBlank(queryDto.getStartTime()) && StrUtil.isNotBlank(queryDto.getEndTime())) {
            qw.between(YsfDmhLayRecordsMz::getSelRetnTime, queryDto.getStartTime(), queryDto.getEndTime());
        }

        // 发送标志
        if (StrUtil.isNotBlank(queryDto.getSend_flag())) {
            qw.eq(YsfDmhLayRecordsMz::getSendFlag, queryDto.getSend_flag());
        }

        final List<YsfDmhLayRecordsMz> list = ysfDmhLayRecordsMzService.list(qw);
        log.info("查询门诊完成，返回{}条记录", list.size());
        log.debug("查询门诊完成，返回数据详情：{}", JSONUtil.toJsonStr(list));
        return list;
    }


    /**
     * 门诊处方查询（基于视图）
     */
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {

            List<YsfDmhLayRecordsMz> mzList = queryOutpatientPrescriptionByView(queryDto);
            if (mzList == null || mzList.isEmpty()) {
                return ApiResult.success(new ArrayList<>());
            }

            // 转换为响应项
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList = mzList.stream()
                    .filter(item -> {
                        String medListCodg = item.getMedListCodg();
                        return medListCodg != null && (medListCodg.startsWith("X") || medListCodg.startsWith("Z"));
                    })
                    .map(this::convertMzEntityToPrescriptionItem)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 处理追溯码库存
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null && !resultList.isEmpty()) {
                handleTracCodgStoreForOutpatient(resultList, userInfo.getAuthorization());
            }

            // 处理任务状态、过滤
            filterByTaskStatusForOutpatient(resultList);

            // 异步保存到3505
            if (!resultList.isEmpty()) {
                nhsa3505Service.save3505Async(resultList);
            }

            return ApiResult.success(resultList);
        } catch (Exception e) {
            log.error("[厦门智业] 门诊处方查询异常", e);
            return ApiResult.error("查询门诊处方发药数据失败：" + e.getMessage());
        }
    }

    /**
     * 按视图查询住院处方
     *
     * @param queryDto 查询dto
     * @return {@link List }<{@link YsfDmhLayRecordsZy }>
     */
    @DS("oracle_his")
    private List<YsfDmhLayRecordsZy> queryInpatientPrescriptionByView(InpatientPrescriptionQueryDto queryDto) {
        if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }
        LambdaQueryWrapper<YsfDmhLayRecordsZy> qw = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(queryDto.getRecordId())) {
            qw.eq(YsfDmhLayRecordsZy::getRecordId, queryDto.getRecordId());
        }

        // 根据药房
        if (StrUtil.isNotBlank(queryDto.getFyyf())) {
            qw.eq(YsfDmhLayRecordsZy::getDeptId, queryDto.getFyyf());
        }
        if (StrUtil.isNotBlank(queryDto.getPatInHosId())) {
            qw.eq(YsfDmhLayRecordsZy::getPatInHosId, queryDto.getPatInHosId());
        }

        if (StrUtil.isNotBlank(queryDto.getStartTime()) && StrUtil.isNotBlank(queryDto.getEndTime())) {
            qw.between(YsfDmhLayRecordsZy::getSelRetnTime, queryDto.getStartTime(), queryDto.getEndTime());
        }
        final List<YsfDmhLayRecordsZy> list = ysfDmhLayRecordsZyService.list(qw);
        log.info("查询住院完成，返回{}条记录", list.size());
        log.debug("查询住院完成，返回数据详情：{}", JSONUtil.toJsonStr(list));
        return list;
    }
    /**
     * 住院处方发药明细（基于视图）
     */
    public ApiResult<List<InPatientDispenseDetailBindScatteredVo>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {
        try {

            List<YsfDmhLayRecordsZy> zyList = queryInpatientPrescriptionByView(queryDto);

            if (zyList == null || zyList.isEmpty()) {
                return ApiResult.success(new ArrayList<>());
            }

            List<InPatientDispenseDetailBindScatteredVo> resultList = zyList.stream()
                    .filter(item -> {
                        String medListCodg = item.getMedListCodg();
                        return medListCodg != null && (medListCodg.startsWith("X") || medListCodg.startsWith("Z"));
                    })
                    .map(this::convertZyEntityToBindVo)
                    .collect(Collectors.toList());

            // 追溯码库存
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            if (userInfo != null) {
                handleTracCodgStoreForInpatient(resultList, userInfo.getAuthorization());
            }

            // 任务状态与任务明细补充
            filterByTaskStatus(resultList, queryDto);

            // 同步保存到3505
            if (!resultList.isEmpty()) {
                nhsa3505Service.saveInpatientDataToNhsa3505(resultList);
            }

            return ApiResult.success(resultList);
        } catch (Exception e) {
            log.error("[厦门智业] 住院处方发药明细查询异常", e);
            return ApiResult.error("查询住院处方发药数据失败：" + e.getMessage());
        }
    }

    /**
     * 同步药品字典（沿用原实现）
     */
    public ApiResult<String> syncDrugDictionary() {
        
    }

    // =============== 内部方法 ===============
    private void validateOutpatientQueryParams(OutpatientPrescriptionQueryDto queryDto) {
        if (StrUtil.isBlank(queryDto.getCardType())) {
            throw new BusinessException("查询类型(cardType)不能为空");
        }
        switch (queryDto.getCardType()) {
            case "1":
                if (StrUtil.isBlank(queryDto.getPatient_id())) {
                    throw new BusinessException("cardType=1时，患者ID(patient_id)不能为空");
                }
                break;
            case "2":
            case "3":
                if (StrUtil.isBlank(queryDto.getCardNo())) {
                    throw new BusinessException("cardType=2或3时，卡号(cardNo)不能为空");
                }
                break;
            case "4":
            case "5":
                if (StrUtil.isBlank(queryDto.getCfxh())) {
                    throw new BusinessException("cardType=4或5时，处方号(cfxh)不能为空");
                }
                break;
            default:
                throw new BusinessException("不支持的查询类型：" + queryDto.getCardType());
        }
    }




    private OutpatientPrescriptionResponseVo.PrescriptionItem convertMzEntityToPrescriptionItem(YsfDmhLayRecordsMz m) {
        try {
            OutpatientPrescriptionResponseVo.PrescriptionItem item = new OutpatientPrescriptionResponseVo.PrescriptionItem();
            item.setMed_list_codg(m.getMedListCodg());
            item.setFixmedins_hilist_id(m.getFixmedinsHilistId());
            item.setFixmedins_hilist_name(m.getFixmedinsHilistName());
            item.setFixmedins_bchno(m.getFixmedinsBchno());
            item.setPrsc_dr_certno(m.getPrscDrCertno());
            item.setPrsc_dr_name(m.getPrscDrName());
            item.setPhar_certno(m.getPharCertno());
            item.setPhar_name(m.getPharName());
            item.setPhar_prac_cert_no(m.getPharPracCertNo());
            item.setMdtrt_sn(m.getMdtrtSn());
            item.setPsn_name(m.getPsnName());
            item.setManu_lotnum(m.getManuLotnum());
            item.setManu_date(m.getManuDate());
            item.setExpy_end(m.getExpyEnd());
            item.setRx_flag(parseStringToInteger(m.getRxFlag()));
            item.setTrdn_flag(parseStringToInteger(m.getTrdnFlag()));
            item.setRxno(m.getRxno());
            item.setRx_circ_flag(m.getRxCircFlag());
            item.setRtal_docno(m.getRtalDocno());
            item.setStoout_no(m.getStooutNo());
            item.setBchno(m.getBchno());
            item.setSel_retn_cnt(m.getSelRetnCnt());
            item.setMin_sel_retn_cnt(m.getMinSelRetnCnt());
            item.setSelRetnUnit(m.getSelRetnUnit());
            item.setHisDosUnit(m.getHisDosUnit());
            item.setHisPacUnit(m.getHisPacUnit());
            item.setSel_retn_time(m.getSelRetnTime());
            item.setSel_retn_opter_name(m.getSelRetnOpterName());
            item.setMdtrt_setl_type(parseStringToInteger(m.getMdtrtSetlType()));
            item.setSpec(m.getSpec());
            item.setProdentp_name(m.getProdentpName());
            item.setCfxh(m.getCfxh());
            item.setCfmxxh(m.getCfmxxh());
            item.setSjh(m.getSjh());
            item.setPatient_id(m.getPatientId());
            item.setHis_con_ratio(m.getHisConRatio());
            item.setSend_flag(parseStringToInteger(m.getSendFlag()));
            item.setSend_time(m.getSendTime());
            item.setDept_id(m.getDeptId());
            item.setDept_name(m.getDeptName());
            item.setWindow(m.getWindowNo());
            return item;
        } catch (Exception e) {
            log.error("[厦门智业] 门诊视图数据转换异常", e);
            return null;
        }
    }

    private InPatientDispenseDetailBindScatteredVo convertZyEntityToBindVo(YsfDmhLayRecordsZy z) {
        InPatientDispenseDetailBindScatteredVo v = new InPatientDispenseDetailBindScatteredVo();
        // 发药记录基本信息
        v.setRecordId(z.getRecordId());
        v.setRecordDetailId(z.getRecordDetailId());
        v.setOriDetailId(z.getOriDetailId());
        v.setIdFee(z.getOrderId());
        v.setNaFee(z.getNaFee());
        v.setSdClassify(z.getSdClassify());
        v.setFgDps(z.getFgDps());
        v.setSendFlag(z.getSendFlag());
        v.setSendTime(z.getSendTime());
        v.setRtalDocno(z.getRtalDocno());
        v.setStooutNo(z.getStooutNo());
        // 病区和科室
        v.setPatWardId(z.getPatWardId());
        v.setPatWardName(z.getPatWardName());
        v.setFyyf(z.getDeptId());
        v.setDeptId(z.getDeptId());
        // 药师信息
        v.setPharCertno(z.getPharCertno());
        v.setPharName(z.getPharName());
        v.setPharPracCertNo(z.getPharPracCertNo());
        v.setSelRetnTime(z.getSelRetnTime());
        // 药品信息
        v.setHisDrugCode(z.getHisDrugCode());
        v.setMedListCodg(z.getMedListCodg());
        v.setSpec(z.getSpec());
        v.setProdentpName(z.getProdentpName());
        v.setFixmedinsHilistId(z.getFixmedinsHilistId());
        v.setFixmedinsHilistName(z.getFixmedinsHilistName());
        v.setManuLotnum(z.getManuLotnum());
        v.setManuDate(z.getManuDate());
        v.setExpyEnd(z.getExpyEnd());
        v.setBchno(z.getBchno());
        v.setRxFlag(z.getRxFlag());
        v.setTrdnFlag(z.getTrdnFlag());
        v.setHisDosUnit(z.getHisDosUnit());
        v.setHisPacUnit(z.getHisPacUnit());
        v.setHisConRatio(z.getHisConRatio());
        // 患者与本次发放明细
        v.setFixmedinsBchno(z.getFixmedinsBchno());
        v.setPatInHosId(z.getPatInHosId());
        v.setMdtrtSn(z.getMdtrtSn());
        v.setBedNo(z.getBedNo());
        v.setPsnNo(z.getPsnNo());
        v.setPsnName(z.getPsnName());
        v.setMdtrtSetlType(z.getMdtrtSetlType());
        v.setOrderId(z.getOrderId());
        v.setPrscDrCertno(z.getPrscDrCertno());
        v.setPrscDrName(z.getPrscDrName());
        if (z.getSelRetnCnt() != null) {
            v.setSelRetnCnt(z.getSelRetnCnt());
            v.setDispCnt(String.valueOf(z.getSelRetnCnt()));
        }
        v.setSelRetnUnit(z.getSelRetnUnit());
        v.setCfxh(z.getCfxh());
        v.setCfmxxh(String.valueOf(z.getCfmxxh()));
        return v;
    }

    // ==== 追溯码库存处理（门诊）====
    private void handleTracCodgStoreForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList, String token) {
        try {
            List<QueryTracDrugRequest> queryTracDrugRequestList = resultList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && StringUtils.isNotEmpty(item.getSel_retn_cnt()))
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedins_hilist_id())
                            .dispCnt(parseIntegerSafely(item.getSel_retn_cnt()))
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                return;
            }

            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
                    item.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
                    item.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));
                    if ("1".equals(queryTracDrug.getIsTrac())) {
                        tracDataList.add(GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(parseIntegerSafely(item.getMin_sel_retn_cnt()))
                                .drugCode(item.getFixmedins_hilist_id())
                                .build());
                    }
                }
            }

            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : resultList) {
                if (item.getTrdn_flag() != null && item.getTrdn_flag() == 1 && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse t = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(t.getDrugCode());
                    item.setDrugTracCodgs(t.getDrugTracCodgs());
                    item.setDispCnt(t.getDispCnt());
                    item.setCurrNum(t.getCurrNum());
                    item.setTracCodgStore(t);
                }
            }
        } catch (Exception e) {
            log.error("[厦门智业] 处理门诊追溯码库存异常", e);
        }
    }

    private Integer parseStringToInteger(String value) {
        try {
            return StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Integer parseIntegerSafely(String value) {
        try {
            return StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : 0;
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    // ==== 门诊任务状态处理 ====
    private void filterByTaskStatusForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> resultList) {
        if (resultList == null || resultList.isEmpty()) return;

        Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = resultList.iterator();
        while (iterator.hasNext()) {
            OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(outPresId)) continue;

            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.zsm.entity.YsfStoTcTask> completedTaskQuery = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
            completedTaskQuery.eq(com.zsm.entity.YsfStoTcTask::getCdBiz, outPresId)
                    .eq(com.zsm.entity.YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(com.zsm.entity.YsfStoTcTask::getDelFlag, "0");
            if (ysfStoTcTaskMapper.selectCount(completedTaskQuery) > 0) {
                iterator.remove();
                continue;
            }

            com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.zsm.entity.YsfStoTcTask> latestTaskQuery = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
            latestTaskQuery.eq(com.zsm.entity.YsfStoTcTask::getCdBiz, outPresId)
                    .in(com.zsm.entity.YsfStoTcTask::getFgStatus, java.util.Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode()))
                    .eq(com.zsm.entity.YsfStoTcTask::getDelFlag, "0")
                    .orderByDesc(com.zsm.entity.YsfStoTcTask::getIdTask)
                    .last("limit 1");

            com.zsm.entity.YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);
            if (latestTask != null) {
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ? latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ?
                        latestTask.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    // ==== 住院追溯码库存处理 ====
    private List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
        try {
            List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
                    .map(item -> QueryTracDrugRequest.builder()
                            .cfxh(item.getCfxh())
                            .cfmxxh(item.getCfmxxh())
                            .drugCode(item.getFixmedinsHilistId())
                            .dispCnt(item.getSelRetnCnt())
                            .build())
                    .collect(Collectors.toList());

            if (queryTracDrugRequestList.isEmpty()) {
                return extendedList;
            }

            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
                    QueryTracDrugResponse q = queryTracDrugMap.get(item.getCfmxxh());
                    item.setTrdnFlag(q.getIsTrac());
                    item.setHisConRatio(String.valueOf(q.getConRatio()));
                    if ("1".equals(q.getIsTrac())) {
                        tracDataList.add(GetTracCodgStoreDataRequest.builder()
                                .cfxh(item.getCfxh())
                                .cfmxxh(item.getCfmxxh())
                                .dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
                                .drugCode(item.getFixmedinsHilistId())
                                .build());
                    }
                }
            }

            Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
            if (!tracDataList.isEmpty()) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token,
                        GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }

            for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
                if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
                    GetTracCodgStoreDataResponse t = tracCodgStoreMap.get(item.getCfmxxh());
                    item.setDrugCode(t.getDrugCode());
                    item.setDrugTracCodgs(t.getDrugTracCodgs());
                    item.setDispCnt(t.getDispCnt());
                    item.setCurrNum(t.getCurrNum());
                    item.setTracCodgStore(t);
                }
            }
        } catch (Exception e) {
            log.error("[厦门智业] 处理住院药品追溯码库存异常", e);
        }
        return extendedList;
    }

    // ==== 住院任务状态与任务明细补充 ====
    private void filterByTaskStatus(List<InPatientDispenseDetailBindScatteredVo> resultList, InpatientPrescriptionQueryDto queryDt) {
        String outPresId = queryDt.getRecordId() + "-" + queryDt.getPatWardId();
        if (StringUtils.isEmpty(outPresId)) {
            return;
        }

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.zsm.entity.YsfStoTcTask> completedTaskQuery = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        completedTaskQuery.eq(com.zsm.entity.YsfStoTcTask::getCdBiz, outPresId)
                .eq(com.zsm.entity.YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                .eq(com.zsm.entity.YsfStoTcTask::getDelFlag, "0");
        // 仅用于过滤，无需保存到局部变量
        ysfStoTcTaskMapper.selectCount(completedTaskQuery);

        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.zsm.entity.YsfStoTcTask> latestTaskQuery = new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        latestTaskQuery.eq(com.zsm.entity.YsfStoTcTask::getCdBiz, outPresId)
                .in(com.zsm.entity.YsfStoTcTask::getFgStatus, java.util.Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode()))
                .eq(com.zsm.entity.YsfStoTcTask::getDelFlag, "0")
                .orderByDesc(com.zsm.entity.YsfStoTcTask::getIdTask)
                .last("limit 1");

        com.zsm.entity.YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);
        if (latestTask != null) {
            final List<com.zsm.entity.YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.lambdaQuery()
                    .eq(com.zsm.entity.YsfStoTcTaskSub::getIdTask, latestTask.getIdTask()).list();

            resultList.forEach(detail -> {
                if (latestTask.getIdTask() != null && StringUtils.isNotEmpty(detail.getRecordDetailId())) {
                    try {
                        com.zsm.entity.YsfStoTcTaskSub taskSub = taskSubList.stream()
                                .filter(item -> item.getCfmxxh().equals(detail.getRecordDetailId())).findFirst().orElse(null);

                        if (taskSub != null) {
                            detail.setTaskIdSubDps(taskSub.getIdSub() != null ? String.valueOf(taskSub.getIdSub()) : null);
                            detail.setTaskDrugtracinfoDps(taskSub.getDrugtracinfo());
                            detail.setTaskFgScannedDps(taskSub.getFgScanned());
                            if (taskSub.getScanTime() != null) {
                                detail.setTaskDetailScanTimeDps(taskSub.getScanTime().toString());
                            }
                        }
                    } catch (Exception e) {
                        log.error("[厦门智业] 查询药品追溯码任务明细异常: {}", e.getMessage(), e);
                    }
                }
            });
        }
    }
}
