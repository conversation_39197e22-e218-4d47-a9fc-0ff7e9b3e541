package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 门诊-退药患者列表视图 V_YSF_DMH_RETURN_PATIENT_MZ
 */
@Data
@TableName("ZHIYDBA.V_YSF_DMH_RETURN_PATIENT_MZ")
@Schema(description = "门诊-退药患者列表视图")
public class YsfDmhReturnPatientMz implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 患者ID */
    @TableField("PATIENT_ID")
    @Schema(description = "患者ID")
    private String patientId;

    /** 患者名称 */
    @TableField("PATIENT_NAME")
    @Schema(description = "患者名称")
    private String patientName;

    /** 销售/退货时间 yyyy-MM-dd HH:mm:ss */
    @TableField("SEL_RETN_TIME")
    @Schema(description = "销售/退货时间 yyyy-MM-dd HH:mm:ss")
    private String selRetnTime;

    /** 收据号 */
    @TableField("SJH")
    @Schema(description = "收据号")
    private String sjh;

    /** 科室ID */
    @TableField("DEPTID")
    @Schema(description = "科室ID")
    private String deptId;

    /** 科室名称 */
    @TableField("DEPTNAME")
    @Schema(description = "科室名称")
    private String deptName;
}


