# Redis缓存使用说明

## 概述

本文档介绍了为项目添加的Redis缓存功能，主要用于缓存SaasHttpUtil.getAccessToken方法获取的用户信息。缓存策略：当天获取一次，第二天自动失效。

## 新增功能

### 1. Redis依赖和配置

#### 1.1 Maven依赖（已添加到pom.xml）
```xml
<!-- Spring Boot Starter Data Redis -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- Lettuce Redis客户端连接池 -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
</dependency>
```

#### 1.2 Redis配置（已添加到application.yml）
```yaml
spring:
  data:
    redis:
      # Redis连接地址
      host: localhost
      # Redis连接端口
      port: 6379
      # Redis数据库索引（默认为0）
      database: 0
      # Redis连接密码（如果设置了密码）
      password:
      # 连接超时时间（毫秒）
      timeout: 3000
      # Lettuce连接池配置
      lettuce:
        pool:
          # 连接池最大连接数（使用负值表示没有限制）
          max-active: 20
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 5
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
```

### 2. 新增工具类和服务

#### 2.1 RedisUtil - Redis工具类
位置：`src/main/java/com/zsm/utils/RedisUtil.java`

提供常用的Redis操作方法：
- `set(key, value, time)` - 设置缓存
- `get(key)` - 获取缓存
- `setUntilMidnight(key, value)` - 设置缓存到当天24点过期
- `hasKey(key)` - 判断key是否存在
- `delete(key)` - 删除缓存
- `getExpire(key)` - 获取过期时间

#### 2.2 TokenCacheService - Token缓存服务
位置：`src/main/java/com/zsm/service/TokenCacheService.java`

专门处理Token缓存的业务逻辑：
- `getTokenFromCache(userAccount)` - 从缓存获取Token
- `cacheToken(userAccount, tokenResponse)` - 缓存Token到当天24点过期
- `clearTokenCache(userAccount)` - 清除Token缓存
- `hasTokenInCache(userAccount)` - 检查Token是否存在
- `getTokenCacheExpire(userAccount)` - 获取Token过期时间

### 3. 修改的现有类

#### 3.1 SaasHttpUtil
- 将类改为Spring组件（添加@Component注解）
- 集成缓存逻辑到原有的`getAccessToken()`静态方法中
- 保持原有调用方式不变，内部自动处理缓存逻辑
- 添加容错机制，Redis不可用时自动降级

#### 3.2 业务代码
- **无需修改任何业务代码**
- 所有现有的`SaasHttpUtil.getAccessToken()`调用都自动享受缓存功能

## 使用方式

### 主要方式：保持原有调用方式（推荐）

**无需修改任何现有业务代码**，缓存功能已自动集成到原有的静态方法中：

```java
@Service
public class YourService {
    
    public void someMethod() {
        // 原有调用方式不变，现在自动带缓存功能
        // 每天第一次调用会请求接口并缓存，后续调用直接从缓存获取
        AccessTokenReponse token = SaasHttpUtil.getAccessToken(
            "username", 
            "password"
        );
        
        // 所有现有业务代码都无需修改，自动享受缓存功能
        AccessTokenReponse token2 = SaasHttpUtil.getAccessToken(
            InpatientAccountConstant.user, 
            InpatientAccountConstant.password
        );
    }
}
```

### 高级方式：直接使用TokenCacheService进行缓存管理

```java
@Service
public class YourService {
    
    @Autowired
    private TokenCacheService tokenCacheService;
    
    public void someMethod() {
        // 检查缓存是否存在
        boolean hasCache = tokenCacheService.hasTokenInCache("username");
        
        // 从缓存获取
        AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache("username");
        
        // 清除缓存
        tokenCacheService.clearTokenCache("username");
        
        // 缓存Token
        tokenCacheService.cacheToken("username", tokenResponse);
        
        // 获取过期时间
        long expireSeconds = tokenCacheService.getTokenCacheExpire("username");
    }
}
```

## 缓存策略

### 缓存键格式
```
saas:token:{userAccount}
```

### 过期时间
- 缓存时间：设置为当天24点过期
- 每天第一次调用时会请求接口并缓存
- 同一天内后续调用直接从缓存获取
- 第二天零点后缓存自动失效，需要重新获取

### 容错机制
- 如果Redis服务不可用，会降级为直接调用接口
- 如果缓存中的Token无效（returnCode != 0），会重新获取
- 异常情况下会记录日志并继续执行

## 环境配置

### 开发环境
请确保Redis服务已启动，默认配置：
- 地址：localhost:6379
- 数据库：0
- 密码：无

### 生产环境
请根据实际环境修改application-prod.yml中的Redis配置：
```yaml
spring:
  data:
    redis:
      host: your-redis-host
      port: 6379
      password: your-redis-password
      database: 0
```

## 监控和调试

### 日志关键词
- "从缓存中获取到有效Token" - 缓存命中
- "缓存中没有有效Token，重新获取" - 缓存未命中
- "新Token已缓存" - Token已缓存
- "强制刷新Token" - 手动刷新缓存

### Redis键查看
可以使用Redis客户端工具查看缓存：
```bash
# 查看所有Token缓存键
redis-cli keys "saas:token:*"

# 查看特定用户的Token
redis-cli get "saas:token:username"

# 查看过期时间
redis-cli ttl "saas:token:username"
```

## 注意事项

1. **Redis服务依赖**：项目启动前请确保Redis服务可用
2. **缓存一致性**：如果需要立即使用新Token，可调用`refreshAccessToken`方法
3. **内存使用**：Token缓存占用很少内存，不会影响系统性能
4. **网络优化**：使用缓存后，同一用户每天只需要调用一次获取Token接口，大大减少网络请求
5. **向后兼容**：原有的`SaasHttpUtil.getAccessToken()`静态方法仍然可用，但不会使用缓存