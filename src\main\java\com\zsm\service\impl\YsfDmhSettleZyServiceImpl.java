package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.entity.YsfDmhSettleZy;
import com.zsm.mapper.YsfDmhSettleZyMapper;
import com.zsm.service.YsfDmhSettleZyService;
import org.springframework.stereotype.Service;

@Service
@DS("oracle_his")
public class YsfDmhSettleZyServiceImpl extends ServiceImpl<YsfDmhSettleZyMapper, YsfDmhSettleZy> implements YsfDmhSettleZyService {
}


