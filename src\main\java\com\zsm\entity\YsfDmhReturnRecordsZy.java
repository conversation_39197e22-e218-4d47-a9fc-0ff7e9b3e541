package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 住院-退药明细视图 V_YSF_DMH_RETURN_RECORDS_ZY
 */
@Data
@TableName("ZHIYDBA.V_YSF_DMH_RETURN_RECORDS_ZY")
@Schema(description = "住院-退药明细视图")
public class YsfDmhReturnRecordsZy implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableField("RECORD_DETAIL_ID")
    @Schema(description = "发药明细id（记录唯一明细）")
    private String recordDetailId;

    @TableField("ID_FEE")
    @Schema(description = "费用ID")
    private String idFee;

    @TableField("NA_FEE")
    @Schema(description = "费用名称")
    private String naFee;

    @TableField("SD_CLASSIFY")
    @Schema(description = "医嘱类别")
    private String sdClassify;

    @TableField("MED_LIST_CODG")
    @Schema(description = "医疗目录编码")
    private String medListCodg;

    @TableField("FIXMEDINS_HILIST_ID")
    @Schema(description = "定点医药机构目录编号（HIS药品唯一值）")
    private String fixmedinsHilistId;

    @TableField("FIXMEDINS_HILIST_NAME")
    @Schema(description = "定点医药机构目录名称（HIS药品名称）")
    private String fixmedinsHilistName;

    @TableField("FIXMEDINS_BCHNO")
    @Schema(description = "定点医药机构批次流水号（记录唯一）")
    private String fixmedinsBchno;

    @TableField("SEL_RETN_CNT")
    @Schema(description = "销售/退货数量")
    private String selRetnCnt;

    @TableField("SEL_RETN_UNIT")
    @Schema(description = "销售/退货单位")
    private String selRetnUnit;

    @TableField("HIS_DOS_UNIT")
    @Schema(description = "HIS 的剂量单位")
    private String hisDosUnit;

    @TableField("HIS_PAC_UNIT")
    @Schema(description = "HIS 的包装单位")
    private String hisPacUnit;

    @TableField("HIS_CON_RATIO")
    @Schema(description = "最小单位转换比")
    private String hisConRatio;

    @TableField("SEL_RETN_TIME")
    @Schema(description = "销售/退货时间 yyyy-MM-dd HH:mm:ss")
    private String selRetnTime;

    @TableField("SEL_RETN_OPTER_NAME")
    @Schema(description = "销售/退货经办人姓名")
    private String selRetnOpterName;

    @TableField("MANU_LOTNUM")
    @Schema(description = "生产批号")
    private String manuLotnum;

    @TableField("MANU_DATE")
    @Schema(description = "生产日期 yyyy-MM-dd")
    private String manuDate;

    @TableField("EXPY_END")
    @Schema(description = "有效期止 yyyy-MM-dd")
    private String expyEnd;

    @TableField("BCHNO")
    @Schema(description = "批次号")
    private String bchno;

    @TableField("RX_FLAG")
    @Schema(description = "处方药标志 0-否 1-是")
    private String rxFlag;

    @TableField("TRDN_FLAG")
    @Schema(description = "拆零标志 0-否 1-是")
    private String trdnFlag;

    @TableField("CFXH")
    @Schema(description = "处方序号")
    private String cfxh;

    @TableField("CFMXXH")
    @Schema(description = "处方明细序号")
    private String cfmxxh;

    @TableField("ORI_CFXH")
    @Schema(description = "原处方序号（退药关联原发药）")
    private String oriCfxh;

    @TableField("ORI_CFMXXH")
    @Schema(description = "原处方明细序号（退药关联原发药）")
    private String oriCfmxxh;

    @TableField("ORI_FIXMEDINS_BCHNO")
    @Schema(description = "原定点机构批次流水号（退药关联原发药）")
    private String oriFixmedinsBchno;

    @TableField("PSN_NO")
    @Schema(description = "人员编号（医保人员编号）")
    private String psnNo;

    @TableField("MDTRT_SN")
    @Schema(description = "就医流水号（医保：MDTRT_ID，自费：院内流水号）")
    private String mdtrtSn;
}


